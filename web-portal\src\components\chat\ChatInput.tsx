import React, { useState, useRef, useCallback } from 'react';
import { Send, Paperclip, Image as ImageIcon, X, Loader2 } from 'lucide-react';
import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { cn } from '../../lib/utils';
import fileUploadService, { FileUploadProgress } from '../../services/fileUploadService';

interface ChatInputProps {
  onSendMessage: (content: string, fileUrl?: string) => void;
  onTyping?: (isTyping: boolean) => void;
  disabled?: boolean;
  placeholder?: string;
  requestId: string;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  onTyping,
  disabled = false,
  placeholder = "Type a message...",
  requestId
}) => {
  const [message, setMessage] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleTyping = useCallback(() => {
    if (!onTyping) return;

    onTyping(true);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      onTyping(false);
    }, 1000);
  }, [onTyping]);

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    handleTyping();
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setUploadProgress(null);
    if (fileInputRef.current) fileInputRef.current.value = '';
    if (imageInputRef.current) imageInputRef.current.value = '';
  };

  const handleSend = async () => {
    if (disabled || isUploading) return;

    const trimmedMessage = message.trim();
    if (!trimmedMessage && !selectedFile) return;

    try {
      let fileUrl: string | undefined;

      // Upload file if selected
      if (selectedFile) {
        setIsUploading(true);
        
        const uploadResult = await fileUploadService.uploadChatFile(
          selectedFile,
          requestId,
          (progress) => setUploadProgress(progress)
        );

        if (uploadResult.success && uploadResult.url) {
          fileUrl = uploadResult.url;
        } else {
          console.error('File upload failed:', uploadResult.error);
          // Still send the message without the file
        }
      }

      // Send message
      onSendMessage(trimmedMessage, fileUrl);

      // Clear input
      setMessage('');
      setSelectedFile(null);
      setUploadProgress(null);
      if (fileInputRef.current) fileInputRef.current.value = '';
      if (imageInputRef.current) imageInputRef.current.value = '';

      // Stop typing indicator
      if (onTyping) onTyping(false);

      // Focus back to textarea
      textareaRef.current?.focus();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const openImageDialog = () => {
    imageInputRef.current?.click();
  };

  const isImageFile = selectedFile?.type.startsWith('image/');

  return (
    <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4">
      {/* File Preview */}
      {selectedFile && (
        <div className="mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isImageFile ? (
                <ImageIcon className="w-5 h-5 text-blue-500" />
              ) : (
                <Paperclip className="w-5 h-5 text-gray-500" />
              )}
              <span className="text-sm font-medium truncate max-w-xs">
                {selectedFile.name}
              </span>
              <span className="text-xs text-gray-500">
                ({fileUploadService.formatFileSize(selectedFile.size)})
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRemoveFile}
              className="h-6 w-6 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Upload Progress */}
          {uploadProgress && uploadProgress.status === 'uploading' && (
            <div className="mt-2">
              <div className="flex items-center gap-2">
                <div className="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress.progress}%` }}
                  />
                </div>
                <span className="text-xs text-gray-500">
                  {Math.round(uploadProgress.progress)}%
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Input Area */}
      <div className="flex items-end gap-2">
        {/* File Upload Buttons */}
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={openImageDialog}
            disabled={disabled || isUploading}
            className="h-10 w-10 p-0"
            title="Upload image"
          >
            <ImageIcon className="w-5 h-5" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={openFileDialog}
            disabled={disabled || isUploading}
            className="h-10 w-10 p-0"
            title="Upload file"
          >
            <Paperclip className="w-5 h-5" />
          </Button>
        </div>

        {/* Message Input */}
        <div className="flex-1">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={handleMessageChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled}
            className="min-h-[40px] max-h-32 resize-none"
            rows={1}
          />
        </div>

        {/* Send Button */}
        <Button
          onClick={handleSend}
          disabled={disabled || isUploading || (!message.trim() && !selectedFile)}
          className="h-10 w-10 p-0"
          title="Send message"
        >
          {isUploading ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <Send className="w-5 h-5" />
          )}
        </Button>
      </div>

      {/* Hidden File Inputs */}
      <input
        ref={fileInputRef}
        type="file"
        accept={fileUploadService.getAllowedFileTypes().join(',')}
        onChange={handleFileSelect}
        className="hidden"
      />
      <input
        ref={imageInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />
    </div>
  );
};

export default ChatInput;
