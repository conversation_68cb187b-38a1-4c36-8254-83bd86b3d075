import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import PermissionGate from '../components/auth/PermissionGate';
import { Permission } from '../types/permissions';
import { 
  Users, 
  FileText, 
  DollarSign, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Activity,
  Wrench
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import FirestoreTestComponent from '../components/FirestoreTestComponent';

// Import request service and types
import requestService from '../services/requestService';
import technicianService from '../services/technicianService';
import serviceService from '../services/serviceService';

// Import types
import { type RequestModel } from '../types/request';
import { type TechnicianModel } from '../types/technician';
import { type ServiceModel } from '../types/service';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const { hints } = usePermissions();

  // Mock data for demonstration
  const stats = {
    totalRequests: 156,
    pendingRequests: 23,
    activeRequests: 8,
    completedToday: 12,
    revenue: 15420,
    activeTechnicians: 5,
    averageResponseTime: '15 min',
    customerSatisfaction: 4.8,
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground mt-2">
          Welcome back, {user?.name}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Requests
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">
              +2 from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {user?.role === 'admin' ? 'Total Technicians' : 'Completed Today'}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{user?.role === 'admin' ? '8' : '5'}</div>
            <p className="text-xs text-muted-foreground">
              {user?.role === 'admin' ? '2 active now' : '+1 from yesterday'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg Response Time
            </CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24 min</div>
            <p className="text-xs text-muted-foreground">
              -5 min from last week
            </p>
          </CardContent>
        </Card>

        {user?.role === 'admin' && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Revenue Today
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$1,240</div>
              <p className="text-xs text-muted-foreground">
                +12% from yesterday
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            {user?.role === 'admin' 
              ? 'Overview of system activity' 
              : 'Your recent service requests'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Activity items */}
            <div className="flex items-center">
              <Badge variant="default" className="mr-4">New</Badge>
              <div className="flex-1 space-y-1">
                <p className="text-sm font-medium leading-none">
                  New request from Ahmed Hassan
                </p>
                <p className="text-sm text-muted-foreground">
                  PC repair - Windows installation
                </p>
              </div>
              <div className="text-sm text-muted-foreground">
                5 min ago
              </div>
            </div>

            <div className="flex items-center">
              <Badge variant="secondary" className="mr-4">Update</Badge>
              <div className="flex-1 space-y-1">
                <p className="text-sm font-medium leading-none">
                  Service completed for Sara Ahmed
                </p>
                <p className="text-sm text-muted-foreground">
                  Laptop screen replacement
                </p>
              </div>
              <div className="text-sm text-muted-foreground">
                1 hour ago
              </div>
            </div>

            <div className="flex items-center">
              <Badge variant="outline" className="mr-4">System</Badge>
              <div className="flex-1 space-y-1">
                <p className="text-sm font-medium leading-none">
                  {user?.role === 'admin' 
                    ? 'New technician joined: Mohamed Ali' 
                    : 'Your profile was updated'}
                </p>
                <p className="text-sm text-muted-foreground">
                  {user?.role === 'admin' 
                    ? 'Specializes in mobile repair' 
                    : 'Phone number changed'}
                </p>
              </div>
              <div className="text-sm text-muted-foreground">
                2 hours ago
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
        <div className="flex gap-4">
          <Card className="flex-1 cursor-pointer hover:bg-accent transition-colors">
            <CardContent className="flex items-center p-4">
              <AlertCircle className="h-5 w-5 mr-3 text-primary" />
              <span className="font-medium">View All Requests</span>
            </CardContent>
          </Card>
          
          {user?.role === 'admin' && (
            <Card className="flex-1 cursor-pointer hover:bg-accent transition-colors">
              <CardContent className="flex items-center p-4">
                <Users className="h-5 w-5 mr-3 text-primary" />
                <span className="font-medium">Manage Technicians</span>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Firestore Test Component - Temporary for testing */}
      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-4">Firestore Connectivity Test</h2>
        <FirestoreTestComponent />
      </div>
    </div>
  );
};

export default DashboardPage; 