import React, { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import requestService from '../services/requestService';
import serviceService from '../services/serviceService';
import technicianService from '../services/technicianService';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
  data?: any;
}

const FirestoreTestComponent: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setTests([]);

    const testResults: TestResult[] = [];

    // Test 1: Get all services
    try {
      setTests([...testResults, { name: 'Get Services', status: 'pending' }]);
      const services = await serviceService.getActiveServices({ limit: 5 });
      testResults.push({
        name: 'Get Services',
        status: 'success',
        message: `Found ${services.length} active services`,
        data: services,
      });
      setTests([...testResults]);
    } catch (error) {
      testResults.push({
        name: 'Get Services',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
      setTests([...testResults]);
    }

    // Test 2: Get pending requests
    try {
      setTests([...testResults, { name: 'Get Pending Requests', status: 'pending' }]);
      const requests = await requestService.getPendingRequests({ limit: 5 });
      testResults.push({
        name: 'Get Pending Requests',
        status: 'success',
        message: `Found ${requests.length} pending requests`,
        data: requests,
      });
      setTests([...testResults]);
    } catch (error) {
      testResults.push({
        name: 'Get Pending Requests',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
      setTests([...testResults]);
    }

    // Test 3: Get available technicians
    try {
      setTests([...testResults, { name: 'Get Available Technicians', status: 'pending' }]);
      const technicians = await technicianService.getAvailableTechnicians({ limit: 5 });
      testResults.push({
        name: 'Get Available Technicians',
        status: 'success',
        message: `Found ${technicians.length} available technicians`,
        data: technicians,
      });
      setTests([...testResults]);
    } catch (error) {
      testResults.push({
        name: 'Get Available Technicians',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
      setTests([...testResults]);
    }

    // Test 4: Get categories
    try {
      setTests([...testResults, { name: 'Get Service Categories', status: 'pending' }]);
      const categories = await serviceService.getCategories();
      testResults.push({
        name: 'Get Service Categories',
        status: 'success',
        message: `Found ${categories.length} categories: ${categories.join(', ')}`,
        data: categories,
      });
      setTests([...testResults]);
    } catch (error) {
      testResults.push({
        name: 'Get Service Categories',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
      setTests([...testResults]);
    }

    // Test 5: Get technician workload stats
    try {
      setTests([...testResults, { name: 'Get Workload Stats', status: 'pending' }]);
      const stats = await technicianService.getWorkloadStats();
      testResults.push({
        name: 'Get Workload Stats',
        status: 'success',
        message: `Available: ${stats.available}, Busy: ${stats.busy}, Total Active: ${stats.totalActive}`,
        data: stats,
      });
      setTests([...testResults]);
    } catch (error) {
      testResults.push({
        name: 'Get Workload Stats',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
      setTests([...testResults]);
    }

    setIsRunning(false);
  };

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Firestore Connectivity Test</CardTitle>
        <CardDescription>
          Test the connection to Firestore and verify that our services are working correctly
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={runTests} 
          disabled={isRunning}
          className="w-full"
        >
          {isRunning ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Running Tests...
            </>
          ) : (
            'Run Connectivity Tests'
          )}
        </Button>

        {tests.length > 0 && (
          <div className="space-y-3">
            {tests.map((test, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 rounded-lg border">
                <div className="flex-shrink-0 mt-0.5">
                  {test.status === 'pending' && (
                    <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                  )}
                  {test.status === 'success' && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                  {test.status === 'error' && (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                <div className="flex-1 space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{test.name}</span>
                    <Badge variant={
                      test.status === 'success' ? 'default' : 
                      test.status === 'error' ? 'destructive' : 
                      'secondary'
                    }>
                      {test.status}
                    </Badge>
                  </div>
                  {test.message && (
                    <p className="text-sm text-muted-foreground">{test.message}</p>
                  )}
                  {test.data && test.status === 'success' && (
                    <details className="text-xs">
                      <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                        View Data
                      </summary>
                      <pre className="mt-2 p-2 bg-muted rounded overflow-auto">
                        {JSON.stringify(test.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FirestoreTestComponent; 