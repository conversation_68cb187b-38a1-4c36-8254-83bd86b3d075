{"rules": {".read": false, ".write": false, "messages": {".read": "auth !== null", ".write": "auth !== null", "$requestId": {".read": "auth !== null", ".write": "auth !== null", "$messageId": {".read": "auth !== null", ".write": "auth !== null", ".validate": "newData.hasChild('content')"}}}, "chats": {".read": "auth !== null", ".write": "auth !== null", "$requestId": {".read": "auth !== null", ".write": "auth !== null"}}, "requests_meta": {".read": "auth !== null", ".write": "auth !== null", "$requestId": {".read": "auth !== null", ".write": "auth !== null"}}, "online_status": {".read": "auth !== null", ".write": "auth !== null", "$userId": {".read": "auth !== null", ".write": "auth !== null"}}, "sessions": {".read": "auth !== null", ".write": "auth !== null", "active": {".read": "auth !== null", ".write": "auth !== null"}}, "service_requests": {".read": "auth !== null", "$requestId": {".read": "auth !== null", ".write": "auth !== null"}}, "requests": {".read": "auth !== null", "$requestId": {".read": "auth !== null", ".write": "auth !== null"}}, "admins": {"$uid": {".read": "auth !== null", ".write": "auth !== null && auth.uid === $uid && root.child('admins').child(auth.uid).exists()"}}}}