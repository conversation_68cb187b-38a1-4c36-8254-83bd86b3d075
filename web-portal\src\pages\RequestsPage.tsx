import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import PermissionGate from '../components/auth/PermissionGate';
import { Permission } from '../types/permissions';
import { 
  FileText, 
  Search, 
  Filter, 
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  User,
  Calendar,
  DollarSign,
  Eye,
  Edit,
  MoreHorizontal
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Import services and types
import requestService from '../services/requestService';
import technicianService from '../services/technicianService';
import serviceService from '../services/serviceService';
import { type RequestModel, RequestStatus } from '../types/request';
import { type TechnicianModel } from '../types/technician';
import { type ServiceModel } from '../types/service';
import StatusUpdateDialog from '../components/requests/StatusUpdateDialog';
import RequestDetailsDialog from '../components/requests/RequestDetailsDialog';

interface RequestFilters {
  status: string;
  technician: string;
  service: string;
  dateRange: string;
  search: string;
  amountMin: string;
  amountMax: string;
  isPaid: string;
}

const RequestsPage: React.FC = () => {
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  
  const [requests, setRequests] = useState<RequestModel[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<RequestModel[]>([]);
  const [technicians, setTechnicians] = useState<TechnicianModel[]>([]);
  const [services, setServices] = useState<ServiceModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Dialog states
  const [selectedRequest, setSelectedRequest] = useState<RequestModel | null>(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  const [filters, setFilters] = useState<RequestFilters>({
    status: 'all',
    technician: 'all',
    service: 'all',
    dateRange: 'all',
    search: '',
    amountMin: '',
    amountMax: '',
    isPaid: 'all'
  });

  // Load data on component mount
  useEffect(() => {
    loadRequests();
    loadTechnicians();
    loadServices();
  }, []);

  // Apply filters when requests or filters change
  useEffect(() => {
    applyFilters();
  }, [requests, filters]);

  const loadRequests = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all requests (admin) or user's requests based on permissions
      let requestsData: RequestModel[];

      if (hasPermission(Permission.VIEW_ALL_REQUESTS)) {
        requestsData = await requestService.getAll({
          orderBy: { field: 'created_at', direction: 'desc' }
        });
      } else if (hasPermission(Permission.VIEW_OWN_REQUESTS) && user?.uid) {
        requestsData = await requestService.getByCustomerId(user.uid);
      } else {
        requestsData = [];
      }

      setRequests(requestsData);
    } catch (err) {
      console.error('Error loading requests:', err);
      setError('Failed to load requests. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadTechnicians = async () => {
    try {
      const techniciansData = await technicianService.getAll({
        orderBy: { field: 'name', direction: 'asc' }
      });
      setTechnicians(techniciansData);
    } catch (err) {
      console.error('Error loading technicians:', err);
    }
  };

  const loadServices = async () => {
    try {
      const servicesData = await serviceService.getActiveServices();
      setServices(servicesData);
    } catch (err) {
      console.error('Error loading services:', err);
    }
  };

  const applyFilters = () => {
    let filtered = [...requests];

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(request => request.status === filters.status);
    }

    // Apply technician filter
    if (filters.technician !== 'all') {
      if (filters.technician === 'unassigned') {
        filtered = filtered.filter(request => !request.technician_id);
      } else {
        filtered = filtered.filter(request => request.technician_id === filters.technician);
      }
    }

    // Apply service filter
    if (filters.service !== 'all') {
      filtered = filtered.filter(request => request.service_id === filters.service);
    }

    // Apply payment status filter
    if (filters.isPaid !== 'all') {
      const isPaidFilter = filters.isPaid === 'paid';
      filtered = filtered.filter(request => request.is_paid === isPaidFilter);
    }

    // Apply amount range filter
    if (filters.amountMin.trim()) {
      const minAmount = parseFloat(filters.amountMin);
      if (!isNaN(minAmount)) {
        filtered = filtered.filter(request => request.amount >= minAmount);
      }
    }
    if (filters.amountMax.trim()) {
      const maxAmount = parseFloat(filters.amountMax);
      if (!isNaN(maxAmount)) {
        filtered = filtered.filter(request => request.amount <= maxAmount);
      }
    }

    // Apply search filter
    if (filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase().trim();
      filtered = filtered.filter(request => {
        const serviceName = getLocalizedText(request.service_name).toLowerCase();
        const serviceDescription = getLocalizedText(request.service_description).toLowerCase();
        const customerIssue = (request.customer_issue || '').toLowerCase();
        const technicianName = (request.technician_name || '').toLowerCase();
        const requestId = request.id.toLowerCase();

        return serviceName.includes(searchTerm) ||
               serviceDescription.includes(searchTerm) ||
               customerIssue.includes(searchTerm) ||
               technicianName.includes(searchTerm) ||
               requestId.includes(searchTerm);
      });
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (filters.dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
      }

      if (filters.dateRange !== 'all') {
        filtered = filtered.filter(request => {
          const requestDate = request.created_at instanceof Date
            ? request.created_at
            : request.created_at.toDate();
          return requestDate >= filterDate;
        });
      }
    }

    setFilteredRequests(filtered);
  };

  const getStatusBadge = (status: RequestStatus) => {
    const statusConfig = {
      [RequestStatus.PAYMENT_PENDING]: { 
        label: 'Payment Pending', 
        variant: 'secondary' as const,
        icon: Clock 
      },
      [RequestStatus.PENDING]: { 
        label: 'Pending', 
        variant: 'outline' as const,
        icon: Clock 
      },
      [RequestStatus.APPROVED]: { 
        label: 'Approved', 
        variant: 'default' as const,
        icon: CheckCircle 
      },
      [RequestStatus.IN_PROGRESS]: { 
        label: 'In Progress', 
        variant: 'default' as const,
        icon: AlertCircle 
      },
      [RequestStatus.COMPLETED]: { 
        label: 'Completed', 
        variant: 'default' as const,
        icon: CheckCircle 
      },
      [RequestStatus.CANCELLED]: { 
        label: 'Cancelled', 
        variant: 'destructive' as const,
        icon: XCircle 
      },
      [RequestStatus.REFUSED]: { 
        label: 'Refused', 
        variant: 'destructive' as const,
        icon: XCircle 
      },
    };

    const config = statusConfig[status];
    const IconComponent = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <IconComponent className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const formatDate = (date: Date | any) => {
    const dateObj = date instanceof Date ? date : date.toDate();
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Helper function to handle multilingual fields
  const getLocalizedText = (text: any, fallback: string = ''): string => {
    // Handle null or undefined
    if (text == null) {
      return fallback;
    }

    // Handle string values
    if (typeof text === 'string') {
      return text;
    }

    // Handle multilingual objects
    if (typeof text === 'object' && text !== null) {
      // Check if it has the expected structure
      if (typeof text.en === 'string' || typeof text.ar === 'string') {
        return text.en || text.ar || fallback;
      }

      // If it's an object but not the expected structure, try to convert to string
      try {
        return String(text) || fallback;
      } catch {
        return fallback;
      }
    }

    // For any other type, try to convert to string
    try {
      return String(text) || fallback;
    } catch {
      return fallback;
    }
  };

  const handleStatusUpdate = (request: RequestModel) => {
    setSelectedRequest(request);
    setIsStatusDialogOpen(true);
  };

  const handleStatusDialogClose = () => {
    setSelectedRequest(null);
    setIsStatusDialogOpen(false);
  };

  const handleRequestUpdated = () => {
    loadRequests(); // Reload requests after update
  };

  const handleViewDetails = (request: RequestModel) => {
    setSelectedRequest(request);
    setIsDetailsDialogOpen(true);
  };

  const handleDetailsDialogClose = () => {
    setSelectedRequest(null);
    setIsDetailsDialogOpen(false);
  };

  const handleEditFromDetails = () => {
    setIsDetailsDialogOpen(false);
    setIsStatusDialogOpen(true);
  };

  const handleQuickStatusUpdate = async (request: RequestModel, newStatus: RequestStatus) => {
    try {
      await requestService.updateStatus(request.id, newStatus);
      loadRequests(); // Reload requests after update
    } catch (error) {
      console.error('Error updating request status:', error);
      // TODO: Show error toast
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading requests...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-destructive mx-auto" />
          <p className="mt-2 text-sm text-destructive">{error}</p>
          <Button onClick={loadRequests} className="mt-4">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold tracking-tight text-gray-900">
            Service Requests
          </h1>
          <p className="text-muted-foreground mt-2 text-lg">
            Manage and track all service requests with advanced filtering and real-time updates
          </p>
        </div>
        
        <PermissionGate permissions={[Permission.CREATE_REQUEST]}>
          <Button className="btn-hover bg-blue-600 hover:bg-blue-700 text-white shadow-lg">
            <Plus className="h-4 w-4 mr-2" />
            New Request
          </Button>
        </PermissionGate>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="card-shadow border-0 bg-blue-50 hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-900">Total Requests</CardTitle>
            <div className="p-2 bg-blue-500 rounded-lg">
              <FileText className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-900">{requests.length}</div>
            <p className="text-xs text-blue-700 mt-1">All time requests</p>
          </CardContent>
        </Card>
        
        <Card className="card-shadow border-0 bg-yellow-50 hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-yellow-900">Pending</CardTitle>
            <div className="p-2 bg-yellow-500 rounded-lg">
              <Clock className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-yellow-900">
              {requests.filter(r => r.status === RequestStatus.PENDING).length}
            </div>
            <p className="text-xs text-yellow-700 mt-1">Awaiting approval</p>
          </CardContent>
        </Card>
        
        <Card className="card-shadow border-0 bg-orange-50 hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-900">In Progress</CardTitle>
            <div className="p-2 bg-orange-500 rounded-lg">
              <AlertCircle className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-900">
              {requests.filter(r => r.status === RequestStatus.IN_PROGRESS).length}
            </div>
            <p className="text-xs text-orange-700 mt-1">Currently active</p>
          </CardContent>
        </Card>

        <Card className="card-shadow border-0 bg-green-50 hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-900">Completed</CardTitle>
            <div className="p-2 bg-green-500 rounded-lg">
              <CheckCircle className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-900">
              {requests.filter(r => r.status === RequestStatus.COMPLETED).length}
            </div>
            <p className="text-xs text-green-700 mt-1">Successfully finished</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="card-shadow-lg border border-gray-200 bg-white">
        <CardHeader className="bg-slate-50 border-b border-gray-200 rounded-t-lg">
          <CardTitle className="flex items-center gap-2 text-slate-800">
            <div className="p-2 bg-slate-600 rounded-lg">
              <Filter className="h-4 w-4 text-white" />
            </div>
            Advanced Filters
          </CardTitle>
          <CardDescription className="text-slate-600">
            Use multiple filters to find exactly what you're looking for
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search requests..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value={RequestStatus.PAYMENT_PENDING}>Payment Pending</SelectItem>
                  <SelectItem value={RequestStatus.PENDING}>Pending</SelectItem>
                  <SelectItem value={RequestStatus.APPROVED}>Approved</SelectItem>
                  <SelectItem value={RequestStatus.IN_PROGRESS}>In Progress</SelectItem>
                  <SelectItem value={RequestStatus.COMPLETED}>Completed</SelectItem>
                  <SelectItem value={RequestStatus.CANCELLED}>Cancelled</SelectItem>
                  <SelectItem value={RequestStatus.REFUSED}>Refused</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Technician</label>
              <Select
                value={filters.technician}
                onValueChange={(value) => setFilters(prev => ({ ...prev, technician: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All technicians" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Technicians</SelectItem>
                  <SelectItem value="unassigned">Unassigned</SelectItem>
                  {technicians.map((technician) => (
                    <SelectItem key={technician.id} value={technician.id}>
                      {getLocalizedText(technician.name, 'Unknown Technician')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Service</label>
              <Select
                value={filters.service}
                onValueChange={(value) => setFilters(prev => ({ ...prev, service: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All services" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Services</SelectItem>
                  {services.map((service) => (
                    <SelectItem key={service.id} value={service.id}>
                      {getLocalizedText(service.name, 'Unknown Service')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Payment Status</label>
              <Select
                value={filters.isPaid}
                onValueChange={(value) => setFilters(prev => ({ ...prev, isPaid: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All payments" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Payments</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="unpaid">Unpaid</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Min Amount</label>
              <Input
                type="number"
                placeholder="0.00"
                value={filters.amountMin}
                onChange={(e) => setFilters(prev => ({ ...prev, amountMin: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Max Amount</label>
              <Input
                type="number"
                placeholder="1000.00"
                value={filters.amountMax}
                onChange={(e) => setFilters(prev => ({ ...prev, amountMax: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Date Range</label>
              <Select
                value={filters.dateRange}
                onValueChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All time" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">Last 7 Days</SelectItem>
                  <SelectItem value="month">Last 30 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-4 flex gap-2">
            <Button
              variant="outline"
              onClick={() => setFilters({
                status: 'all',
                technician: 'all',
                service: 'all',
                dateRange: 'all',
                search: '',
                amountMin: '',
                amountMax: '',
                isPaid: 'all'
              })}
            >
              Clear All Filters
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                loadRequests();
                loadTechnicians();
                loadServices();
              }}
            >
              Refresh Data
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Requests Table */}
      <Card className="card-shadow-lg border border-gray-200 bg-white">
        <CardHeader className="bg-indigo-50 border-b border-gray-200 rounded-t-lg">
          <CardTitle className="flex items-center gap-2 text-indigo-900">
            <div className="p-2 bg-indigo-600 rounded-lg">
              <FileText className="h-4 w-4 text-white" />
            </div>
            Service Requests ({filteredRequests.length})
          </CardTitle>
          <CardDescription className="text-indigo-700">
            {filteredRequests.length === requests.length
              ? 'Showing all requests with real-time updates'
              : `Showing ${filteredRequests.length} of ${requests.length} requests based on your filters`
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredRequests.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No requests found</h3>
              <p className="text-muted-foreground">
                {requests.length === 0
                  ? 'No requests have been created yet.'
                  : 'Try adjusting your filters to see more results.'
                }
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Request ID</TableHead>
                    <TableHead>Service</TableHead>
                    <TableHead>Customer Issue</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Payment</TableHead>
                    <TableHead>Technician</TableHead>
                    <TableHead>AnyDesk ID</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRequests.map((request) => (
                    <TableRow key={request.id}>
                        <TableCell className="font-medium">
                          #{(request.id || '').slice(-8)}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {getLocalizedText(request.service_name, 'Unknown Service')}
                            </div>
                            <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                              {getLocalizedText(request.service_description, 'No description')}
                            </div>
                          </div>
                        </TableCell>
                      <TableCell>
                        <div className="max-w-[200px] truncate" title={request.customer_issue || ''}>
                          {request.customer_issue || 'No issue description'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusBadge(request.status)}
                          {/* Quick action buttons for certain statuses */}
                          <PermissionGate permissions={[Permission.UPDATE_REQUEST]}>
                            {request.status === RequestStatus.PENDING && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuickStatusUpdate(request, RequestStatus.APPROVED)}
                                className="h-6 px-2 text-xs"
                              >
                                Approve
                              </Button>
                            )}
                            {request.status === RequestStatus.APPROVED && !request.technician_id && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleStatusUpdate(request)}
                                className="h-6 px-2 text-xs"
                              >
                                Assign
                              </Button>
                            )}
                            {request.status === RequestStatus.APPROVED && request.technician_id && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuickStatusUpdate(request, RequestStatus.IN_PROGRESS)}
                                className="h-6 px-2 text-xs"
                              >
                                Start
                              </Button>
                            )}
                            {request.status === RequestStatus.IN_PROGRESS && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuickStatusUpdate(request, RequestStatus.COMPLETED)}
                                className="h-6 px-2 text-xs"
                              >
                                Complete
                              </Button>
                            )}
                          </PermissionGate>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={request.is_paid ? 'default' : 'secondary'}>
                          {request.is_paid ? 'Paid' : 'Unpaid'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {request.technician_name ? (
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            {request.technician_name}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Unassigned</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {request.anydesk_id || request.anydeskId ? (
                          <div className="flex items-center gap-2">
                            <code className="bg-muted px-2 py-1 rounded text-sm font-mono">
                              {request.anydesk_id || request.anydeskId}
                            </code>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                const anydeskId = request.anydesk_id || request.anydeskId;
                                if (anydeskId) {
                                  navigator.clipboard.writeText(anydeskId);
                                  // TODO: Show toast notification
                                }
                              }}
                              className="h-6 w-6 p-0"
                              title="Copy AnyDesk ID"
                            >
                              <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </Button>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Not provided</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                          {formatCurrency(request.amount)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          {formatDate(request.created_at)}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleViewDetails(request)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <PermissionGate permissions={[Permission.UPDATE_REQUEST]}>
                              <DropdownMenuItem onClick={() => handleStatusUpdate(request)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Update Status
                              </DropdownMenuItem>
                            </PermissionGate>
                            <PermissionGate permissions={[Permission.ASSIGN_REQUEST]}>
                              <DropdownMenuItem onClick={() => handleStatusUpdate(request)}>
                                <User className="mr-2 h-4 w-4" />
                                Assign Technician
                              </DropdownMenuItem>
                            </PermissionGate>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => navigator.clipboard.writeText(request.id)}>
                              Copy Request ID
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Request Details Dialog */}
      <RequestDetailsDialog
        request={selectedRequest}
        isOpen={isDetailsDialogOpen}
        onClose={handleDetailsDialogClose}
        onEdit={handleEditFromDetails}
      />

      {/* Status Update Dialog */}
      <StatusUpdateDialog
        request={selectedRequest}
        technicians={technicians}
        isOpen={isStatusDialogOpen}
        onClose={handleStatusDialogClose}
        onUpdate={handleRequestUpdated}
      />
    </div>
  );
};

export default RequestsPage;
