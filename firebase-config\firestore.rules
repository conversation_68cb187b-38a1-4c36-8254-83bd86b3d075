rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && (
        // Support both implementations: dedicated collection or role field
        (exists(/databases/$(database)/documents/admins/$(request.auth.uid))) ||
        (exists(/databases/$(database)/documents/users/$(request.auth.uid)) && 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin')
      );
    }
    
    function isTechnician() {
      return isAuthenticated() && (
        // Support both implementations: dedicated collection or role field
        (exists(/databases/$(database)/documents/technicians/$(request.auth.uid))) ||
        (exists(/databases/$(database)/documents/users/$(request.auth.uid)) && 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'technician')
      );
    }
    
    function isFirstAdmin() {
      return isAuthenticated() && 
        !exists(/databases/$(database)/documents/admins/first-admin-marker) &&
        request.resource.data.role == 'admin';
    }
    
    // Device tokens collection
    match /device_tokens/{tokenId} {
      allow read: if isAuthenticated() && (
        isAdmin() || 
        isOwner(resource.data.userId) ||
        (isTechnician() && resource.data.userId == request.auth.uid)
      );
      allow create, update: if isAuthenticated() && (
        request.resource.data.userId == request.auth.uid
      );
      allow delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAdmin()
      );
    }
    
    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated() && (
        isOwner(userId) || 
        isAdmin() ||
        isTechnician()
      );
      allow create: if isAuthenticated() && (
        isOwner(userId) || 
        isAdmin()
      );
      allow update: if isAuthenticated() && (
        (isOwner(userId) && !request.resource.data.diff(resource.data).affectedKeys().hasAny(['role'])) ||
        isAdmin()
      );
      allow delete: if isAdmin();
      
      // Nested collections
      match /notifications/{notificationId} {
        allow read: if isOwner(userId);
        allow write: if isAuthenticated() && (
          isOwner(userId) ||
          isAdmin()
        );
      }
    }
    
    // Combined rules for requests/service_requests
    // Each implementation uses different field names, so we account for both
    match /requests/{requestId} {
      allow read: if isAuthenticated() && (
        isAdmin() ||
        (isTechnician() && (resource.data.assignedTo == request.auth.uid || resource.data.assignedTo == null)) ||
        (isAuthenticated() && resource.data.clientId == request.auth.uid)
      );
      allow create: if isAuthenticated() && request.resource.data.clientId == request.auth.uid;
      allow update: if isAdmin() ||
                     (isTechnician() && resource.data.assignedTo == request.auth.uid);
      allow delete: if isAdmin();
      
      // Add support for messages subcollection
      match /messages/{messageId} {
        allow read: if isAuthenticated();
        allow create: if isAuthenticated() && (
          // Allow system messages regardless of chat status
          (request.resource.data.senderType == 'system' || request.resource.data.sender_type == 'system') ||
          
          // Allow admins to send messages as technicians
          isAdmin() ||
          
          // For other messages, check if user is a participant
          (
            // Customer messages - check multiple field name variants
            (
              (request.resource.data.senderType == 'customer' || request.resource.data.sender_type == 'customer') && (
                request.auth.uid == get(/databases/$(database)/documents/requests/$(requestId)).data.clientId ||
                request.auth.uid == get(/databases/$(database)/documents/requests/$(requestId)).data.customer_id ||
                request.auth.uid == get(/databases/$(database)/documents/requests/$(requestId)).data.customerId
              )
            ) ||
            
            // Technician messages - check multiple field name variants
            (
              (request.resource.data.senderType == 'technician' || request.resource.data.sender_type == 'technician') && (
                request.auth.uid == get(/databases/$(database)/documents/requests/$(requestId)).data.assignedTo ||
                request.auth.uid == get(/databases/$(database)/documents/requests/$(requestId)).data.technician_id ||
                request.auth.uid == get(/databases/$(database)/documents/requests/$(requestId)).data.technicianId ||
                isTechnician()
              )
            )
          )
        );
        allow update: if isAuthenticated();
        allow delete: if isAdmin();
      }
    }
    
    // Support mobile app service_requests collection
    match /service_requests/{requestId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update: if isAuthenticated();
      allow delete: if isAdmin();

      match /messages/{messageId} {
        allow read: if isAuthenticated();
        allow create: if isAuthenticated() && (
          // Allow system messages regardless of chat status
          (request.resource.data.senderType == 'system' || request.resource.data.sender_type == 'system') ||
          
          // Allow admins to send messages as technicians
          isAdmin() ||
          
          // For other messages, check if user is a participant
          (
            // Customer messages - check multiple field name variants
            (
              (request.resource.data.senderType == 'customer' || request.resource.data.sender_type == 'customer') && (
                request.auth.uid == get(/databases/$(database)/documents/service_requests/$(requestId)).data.customer_id ||
                request.auth.uid == get(/databases/$(database)/documents/service_requests/$(requestId)).data.customerId ||
                request.auth.uid == get(/databases/$(database)/documents/service_requests/$(requestId)).data.clientId
              )
            ) ||
            
            // Technician messages - check multiple field name variants
            (
              (request.resource.data.senderType == 'technician' || request.resource.data.sender_type == 'technician') && (
                request.auth.uid == get(/databases/$(database)/documents/service_requests/$(requestId)).data.technician_id ||
                request.auth.uid == get(/databases/$(database)/documents/service_requests/$(requestId)).data.technicianId ||
                request.auth.uid == get(/databases/$(database)/documents/service_requests/$(requestId)).data.assignedTo ||
                isTechnician()
              )
            )
          )
        );
        allow update: if isAuthenticated();
        allow delete: if isAdmin();
      }
    }
    
    // Admins collection
    match /admins/{adminId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isFirstAdmin();
    }
    
    // Technicians collection
    match /technicians/{techId} {
      allow read: if isAuthenticated();
      allow create: if isAdmin() ||
                     (isAuthenticated() &&
                      request.auth.token.admin == true) ||
                     (isAuthenticated() &&
                      request.auth.uid == techId &&
                      exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'technician');
      allow update: if isAdmin() || (isAuthenticated() && request.auth.uid == techId && 
        request.resource.data.diff(resource.data).affectedKeys().hasAny(['status', 'is_available', 'updated_at']));
      allow delete: if isAdmin();
    }
    
    // Notifications
    match /notifications/{notificationId} {
      allow read: if isAuthenticated() && (
        isAdmin() ||
        resource.data.userId == request.auth.uid
      );
      allow create: if isAuthenticated() && (
        request.resource.data.userId == request.auth.uid ||
        isAdmin() ||
        isTechnician()
      );
      allow update: if isAuthenticated() && (
        isAdmin() ||
        resource.data.userId == request.auth.uid
      );
      allow delete: if isAdmin();
    }
    
    // Special document to track first admin creation
    match /admins/first-admin-marker {
      allow read: if isAuthenticated();
      allow create: if isFirstAdmin();
    }
    
    // Services collection 
    match /services/{serviceId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // App configuration - read only for all users, writable by admins
    match /app_config/{configId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Metadata collection - readable by all authenticated users, writable by admins
    match /metadata/{document=**} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Payment transactions
    match /payment_transactions/{transactionId} {
      allow read: if isAuthenticated() && (
        resource.data.customer_id == request.auth.uid || 
        resource.data.customerId == request.auth.uid || 
        isAdmin() ||
        (isTechnician() && exists(/databases/$(database)/documents/service_requests/$(resource.data.requestId)) && (
          get(/databases/$(database)/documents/service_requests/$(resource.data.requestId)).data.technician_id == request.auth.uid ||
          get(/databases/$(database)/documents/service_requests/$(resource.data.requestId)).data.technicianId == request.auth.uid
        ))
      );
      allow create: if isAuthenticated();
      allow update: if isAdmin();
    }
    
    // Reviews collection
    match /reviews/{reviewId} {
      allow read: if true;
      
      // Combine both review creation rules
      allow create: if isAuthenticated() && (
        // Web portal style
        (exists(/databases/$(database)/documents/requests/$(request.resource.data.requestId)) &&
         get(/databases/$(database)/documents/requests/$(request.resource.data.requestId)).data.clientId == request.auth.uid) ||
        // Mobile app style
        (request.resource.data.customerId == request.auth.uid || 
         request.resource.data.customer_id == request.auth.uid)
      );
      
      allow update, delete: if isAdmin();
    }
    
    // Activities collection - needed for the dashboard
    match /activities/{activityId} {
      allow read: if isAuthenticated() && (
        isAdmin() ||
        isTechnician() ||
        (resource.data.userId != null && resource.data.userId == request.auth.uid)
      );
      // Require a timestamp field for ordering
      allow create: if isAuthenticated() && request.resource.data.timestamp != null;
      allow update, delete: if isAdmin();
    }
    
    // Sessions collection - for session management
    match /sessions/{sessionId} {
      // Users can read their own sessions, admins can read all
      allow read: if isAuthenticated() && (
        isAdmin() ||
        resource.data.uid == request.auth.uid
      );
      
      // Users can create/update their own sessions
      allow create, update: if isAuthenticated() && (
        request.resource.data.uid == request.auth.uid
      );
      
      // Users can delete their own sessions, admins can delete any
      allow delete: if isAuthenticated() && (
        isAdmin() ||
        resource.data.uid == request.auth.uid
      );
    }
    
    // Add explicit rules for serviceRequests collection
    match /serviceRequests/{requestId} {
      allow read: if isAuthenticated() && (
        isAdmin() ||
        isTechnician() ||
        resource.data.customerId == request.auth.uid ||
        resource.data.customer_id == request.auth.uid
      );
      allow write: if isAdmin();
    }
    
    // Add explicit rules for ratings collection
    match /ratings/{ratingId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && (
        isAdmin() ||
        request.resource.data.customerId == request.auth.uid ||
        request.resource.data.customer_id == request.auth.uid
      );
    }
    
    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
} 