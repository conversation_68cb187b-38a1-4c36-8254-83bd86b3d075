import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/database_service.dart';
import '../services/search_service.dart';
import '../models/service_model.dart';
import 'request_details_screen.dart';
import '../utils/navigation_utils.dart';
import '../widgets/text_styles.dart';

class ServicesScreen extends StatefulWidget {
  const ServicesScreen({super.key});

  @override
  State<ServicesScreen> createState() => _ServicesScreenState();
}

class _ServicesScreenState extends State<ServicesScreen>
    with SingleTickerProviderStateMixin {
  String _searchQuery = '';
  bool _isLoading = true;
  String? _selectedCategory;
  late AnimationController _animationController;
  final TextEditingController _searchController = TextEditingController();

  // Service data - loaded from Firestore
  List<ServiceModel> _services = [];
  List<String> _categories = [];

  // Enhanced search service
  final SearchService _searchService = SearchService();

  @override
  void initState() {
    super.initState();
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animationController.forward();

    // Initialize search service
    _initializeSearchService();

    // Load services from database
    _loadServices();
  }

  Future<void> _initializeSearchService() async {
    try {
      await _searchService.initialize();
    } catch (e) {
      debugPrint('Error initializing search service: $e');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadServices() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get database service
      final databaseService = Provider.of<DatabaseService>(
        context,
        listen: false,
      );

      // Load active services
      _services = await databaseService.getActiveServices();

      // Extract unique categories
      _categories = _services.map((s) => s.category).toSet().toList();
      _categories.sort(); // Sort alphabetically
    } catch (e) {
      debugPrint('Error loading services: $e');
      // Handle the error, perhaps show a snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _translate('Failed to load services. Please try again.'),
          ),
          action: SnackBarAction(
            label: _translate('Retry'),
            onPressed: _loadServices,
          ),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Helper method to get translations
  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(key);
  }

  // Get icon for service category
  IconData _getIconForService(ServiceModel service) {
    if (service.metadata != null && service.metadata!.containsKey('icon')) {
      // Get icon from metadata
      final dynamic iconData = service.metadata!['icon'];
      // Return the icon if it's already an IconData
      if (iconData is IconData) {
        return iconData;
      }

      // If it's a string, map it to an IconData
      if (iconData is String) {
        // Map of common icon names to IconData
        final Map<String, IconData> iconMap = {
          'computer': Icons.computer_rounded,
          'article': Icons.article_rounded,
          'print': Icons.print_rounded,
          'wifi': Icons.wifi_rounded,
          'security': Icons.security_rounded,
          'backup': Icons.backup_rounded,
          'storage': Icons.storage_rounded,
          'devices': Icons.devices_rounded,
        };

        return iconMap[iconData] ?? Icons.computer_rounded;
      }
    }

    // Default icon based on category
    final Map<String, IconData> categoryIcons = {
      'os': Icons.computer_rounded,
      'productivity': Icons.article_rounded,
      'hardware': Icons.devices_other_rounded,
      'network': Icons.wifi_rounded,
      'security': Icons.security_rounded,
      'data': Icons.storage_rounded,
    };

    return categoryIcons[service.category] ??
        Icons.miscellaneous_services_rounded;
  }

  // Filter services using enhanced search service
  List<ServiceModel> get _filteredServices {
    // Get the current language code for translations
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final languageCode = translationService.currentLocale.languageCode;

    // Use enhanced search service for better filtering and sorting
    return _searchService.searchServices(
      services: _services,
      query: _searchQuery,
      languageCode: languageCode,
      categories: _selectedCategory != null ? [_selectedCategory!] : null,
      sortBy: ServiceSortOption.relevance,
    );
  }

  Color _getCategoryColor(String category) {
    // Map categories to consistent colors
    final Map<String, Color> categoryColors = {
      'os': Colors.blue,
      'productivity': Colors.green,
      'hardware': Colors.orange,
      'network': Colors.purple,
      'security': Colors.red,
      'data': Colors.teal,
    };

    return categoryColors[category] ?? Colors.blueGrey;
  }

  // Helper method to ensure service descriptions are properly translated
  String _getTranslatedDescription(ServiceModel service) {
    if (service.description.isEmpty) {
      return '';
    }

    // Get the current language code from the translation service
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final languageCode = translationService.currentLocale.languageCode;

    // Use the new getTranslatedDescription method
    return service.getTranslatedDescription(languageCode);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isRtl = Provider.of<TranslationService>(context, listen: false).isRtl;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Floating header
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            leading: BackButton(color: colorScheme.onSurface),
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 56), // Space for back button
                      Expanded(
                        child: Text(
                          _translate('Select Service'),
                          style: AppTextStyles.headingMedium(
                            context,
                            color: colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 56), // Balance the layout
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Main content
          SliverToBoxAdapter(
            child: Column(
              children: [
                // Search field with light border
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: TextField(
                    controller: _searchController,
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                    style: AppTextStyles.bodyMedium(context),
                    decoration: InputDecoration(
                      hintText: _translate('Search services...'),
                      hintStyle: AppTextStyles.bodyMedium(
                        context,
                        color: colorScheme.onSurface.withOpacity(0.6),
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        color: colorScheme.onSurface.withOpacity(0.6),
                      ),
                      suffixIcon:
                          _searchQuery.isNotEmpty
                              ? IconButton(
                                icon: Icon(
                                  Icons.clear,
                                  color: colorScheme.onSurface.withOpacity(0.6),
                                ),
                                onPressed: () {
                                  _searchController.clear();
                                  setState(() {
                                    _searchQuery = '';
                                  });
                                },
                              )
                              : null,
                      filled: true,
                      fillColor: colorScheme.surface,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colorScheme.primary,
                          width: 2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 16,
                      ),
                    ),
                  ),
                ),

                // Category filters with light borders
                if (_categories.isNotEmpty)
                  SizedBox(
                    height: 50,
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      scrollDirection: Axis.horizontal,
                      itemCount: _categories.length + 1, // +1 for "All" option
                      itemBuilder: (context, index) {
                        // First item is "All"
                        if (index == 0) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              selected: _selectedCategory == null,
                              label: Text(_translate('All')),
                              labelStyle: AppTextStyles.bodyMedium(
                                context,
                                color:
                                    _selectedCategory == null
                                        ? Colors.white
                                        : colorScheme.onSurface,
                              ),
                              backgroundColor: colorScheme.surface,
                              selectedColor: colorScheme.primary,
                              side: BorderSide(color: Colors.grey.shade300),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              onSelected: (selected) {
                                setState(() {
                                  _selectedCategory = null;
                                });
                              },
                            ),
                          );
                        }

                        final category = _categories[index - 1];
                        final isSelected = _selectedCategory == category;

                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            selected: isSelected,
                            label: Text(_translate(category.toUpperCase())),
                            labelStyle: AppTextStyles.bodyMedium(
                              context,
                              color:
                                  isSelected
                                      ? Colors.white
                                      : colorScheme.onSurface,
                            ),
                            backgroundColor: colorScheme.surface,
                            selectedColor: _getCategoryColor(category),
                            side: BorderSide(color: Colors.grey.shade300),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            onSelected: (selected) {
                              setState(() {
                                _selectedCategory = selected ? category : null;
                              });
                            },
                          ),
                        );
                      },
                    ),
                  ),

                const SizedBox(height: 16),
              ],
            ),
          ),

          // Services list
          _isLoading
              ? const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
              : _filteredServices.isEmpty
              ? SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.search_off_rounded,
                          size: 48,
                          color: colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _translate('No services found'),
                        style: AppTextStyles.headingSmall(
                          context,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _translate('Try adjusting your search or filters'),
                        style: AppTextStyles.bodyMedium(
                          context,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      if (_selectedCategory != null ||
                          _searchQuery.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        OutlinedButton.icon(
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _selectedCategory = null;
                              _searchQuery = '';
                            });
                          },
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: Colors.grey.shade300),
                          ),
                          icon: const Icon(Icons.filter_list_off),
                          label: Text(_translate('Clear filters')),
                        ),
                      ],
                    ],
                  ),
                ),
              )
              : SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final service = _filteredServices[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: _buildServiceCard(service, isRtl),
                    );
                  }, childCount: _filteredServices.length),
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildServiceCard(ServiceModel service, bool isRtl) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final categoryColor = _getCategoryColor(service.category);

    // Get the current language code from the translation service
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final languageCode = translationService.currentLocale.languageCode;

    // Ensure proper translation of service name and description
    final translatedName = service.getTranslatedName(languageCode);
    final translatedDescription = _getTranslatedDescription(service);

    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: InkWell(
        onTap: () {
          NavigationUtils.navigateKeepingStack(
            context,
            RequestDetailsScreen(service: service),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Service icon with background
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: categoryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getIconForService(service),
                      size: 24,
                      color: categoryColor,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Service name and category
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Service name
                        Text(
                          translatedName,
                          style: AppTextStyles.headingSmall(
                            context,
                            color: colorScheme.onSurface,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),

                        // Category badge
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: categoryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: categoryColor.withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            _translate(service.category.toUpperCase()),
                            style: AppTextStyles.bodySmall(
                              context,
                              color: categoryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Duration
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 16,
                        color: colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '${service.estimatedDuration} ${_translate('min')}',
                        style: AppTextStyles.bodySmall(
                          context,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Description
              if (translatedDescription.isNotEmpty)
                Text(
                  translatedDescription,
                  style: AppTextStyles.bodyMedium(
                    context,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

              const SizedBox(height: 16),

              // Bottom row with price and request button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Price
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _translate('Starting from'),
                        style: AppTextStyles.bodySmall(
                          context,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        '${_translate('L.E')} ${service.basePrice.toStringAsFixed(0)}',
                        style: AppTextStyles.headingSmall(
                          context,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),

                  // Request button
                  FilledButton(
                    onPressed: () {
                      NavigationUtils.navigateKeepingStack(
                        context,
                        RequestDetailsScreen(service: service),
                      );
                    },
                    style: FilledButton.styleFrom(
                      backgroundColor: colorScheme.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                    child: Text(
                      _translate('Select'),
                      style: AppTextStyles.buttonMedium(
                        context,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
