rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isSignedIn() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isTechnician() {
      return isSignedIn() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'technician';
    }
    
    function isCustomerService() {
      return isSignedIn() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'customer_service';
    }
    
    function isOwner(userId) {
      return isSignedIn() && request.auth.uid == userId;
    }
    
    // All users can read/write their own document
    match /users/{userId} {
      allow read: if isSignedIn();
      allow write: if isOwner(userId) || isAdmin();
    }
    
    // Ad<PERSON>, technician, and customer service can access all service requests
    match /service_requests/{requestId} {
      allow read: if isSignedIn();
      allow write: if isAdmin() || isTechnician() || isCustomerService() || 
                    (isSignedIn() && request.resource.data.userId == request.auth.uid);
    }
    
    // Everyone can read metadata, only admin can write
    match /metadata/{document=**} {
      allow read: if isSignedIn();
      allow write: if isAdmin();
    }
    
    // Admin and technicians can access ratings
    match /ratings/{ratingId} {
      allow read: if isSignedIn();
      allow write: if isAdmin() || 
                    (isSignedIn() && request.resource.data.userId == request.auth.uid);
    }
    
    // Admin can access all documents
    match /{document=**} {
      allow read, write: if isAdmin();
    }
  }
} 