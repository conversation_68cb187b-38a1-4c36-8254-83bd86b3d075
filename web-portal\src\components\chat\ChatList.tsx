import React, { useState, useEffect } from 'react';
import { MessageCircle, User, Clock, Search, Loader2 } from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { useAuth } from '../../contexts/AuthContext';
import { RequestModel } from '../../types/request';
import chatService from '../../services/chatService';
import { Input } from '../ui/input';
import { cn } from '../../lib/utils';

interface ChatListItem {
  id: string;
  requestId: string;
  request: RequestModel;
  lastMessage?: any;
  unreadCount: number;
  isOnline: boolean;
}

interface ChatListProps {
  onChatSelect: (request: RequestModel) => void;
  selectedChatId?: string;
  className?: string;
}

const ChatList: React.FC<ChatListProps> = ({
  onChatSelect,
  selectedChatId,
  className
}) => {
  const { user } = useAuth();
  const [chats, setChats] = useState<ChatListItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Load active chats
  useEffect(() => {
    if (!user) return;

    const loadChats = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const result = await chatService.getActiveChats(user.uid, user.role);
        
        if (result.success && result.data) {
          setChats(result.data);
        } else {
          setError(result.error?.message || 'Failed to load chats');
        }
      } catch (error) {
        console.error('Error loading chats:', error);
        setError('Failed to load chats');
      } finally {
        setIsLoading(false);
      }
    };

    loadChats();

    // Refresh chats every 30 seconds
    const interval = setInterval(loadChats, 30000);

    return () => clearInterval(interval);
  }, [user]);

  // Filter chats based on search query
  const filteredChats = chats.filter(chat => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    const serviceName = typeof chat.request.service_name === 'string' 
      ? chat.request.service_name 
      : chat.request.service_name?.en || '';
    const customerName = chat.request.customer_name || '';
    const technicianName = chat.request.technician_name || '';
    const requestId = chat.request.id;

    return (
      serviceName.toLowerCase().includes(query) ||
      customerName.toLowerCase().includes(query) ||
      technicianName.toLowerCase().includes(query) ||
      requestId.toLowerCase().includes(query)
    );
  });

  const formatLastMessageTime = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return format(date, 'HH:mm');
    } else if (diffInHours < 168) { // 7 days
      return format(date, 'EEE');
    } else {
      return format(date, 'MMM dd');
    }
  };

  const getLastMessagePreview = (lastMessage: any) => {
    if (!lastMessage) return 'No messages yet';
    
    if (lastMessage.messageType === 'image') {
      return '📷 Image';
    } else if (lastMessage.messageType === 'file') {
      return '📎 File';
    } else if (lastMessage.messageType === 'system') {
      return lastMessage.content;
    } else {
      return lastMessage.content || 'Message';
    }
  };

  const getParticipantName = (chat: ChatListItem) => {
    if (user?.role === 'customer') {
      return chat.request.technician_name || 'Technician';
    } else {
      return chat.request.customer_name || 'Customer';
    }
  };

  const getServiceName = (serviceName: string | { en: string; ar: string }) => {
    if (typeof serviceName === 'string') {
      return serviceName;
    }
    return serviceName?.en || 'Service';
  };

  if (isLoading) {
    return (
      <div className={cn("flex flex-col h-full bg-white dark:bg-gray-800", className)}>
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Chats
          </h2>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-2 text-gray-500">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>Loading chats...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex flex-col h-full bg-white dark:bg-gray-800", className)}>
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Chats
          </h2>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-500 mb-2">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="text-blue-500 hover:underline"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-white dark:bg-gray-800", className)}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
          Chats
        </h2>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search chats..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {filteredChats.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <MessageCircle className="w-12 h-12 mb-4 opacity-50" />
            <p className="text-center">
              {searchQuery ? 'No chats found' : 'No active chats'}
            </p>
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="text-blue-500 hover:underline mt-2"
              >
                Clear search
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredChats.map((chat) => (
              <div
                key={chat.id}
                onClick={() => onChatSelect(chat.request)}
                className={cn(
                  "p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                  selectedChatId === chat.requestId && "bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500"
                )}
              >
                <div className="flex items-start gap-3">
                  {/* Avatar */}
                  <div className="flex-shrink-0 relative">
                    <div className="w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                      <User className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                    </div>
                    {chat.isOnline && (
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800" />
                    )}
                  </div>

                  {/* Chat Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                        {getParticipantName(chat)}
                      </h3>
                      <span className="text-xs text-gray-500">
                        {formatLastMessageTime(chat.lastMessage?.createdAt)}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate mb-1">
                      {getServiceName(chat.request.service_name)}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-500 truncate flex-1">
                        {getLastMessagePreview(chat.lastMessage)}
                      </p>
                      
                      {chat.unreadCount > 0 && (
                        <span className="ml-2 bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                          {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatList;
