import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AnyDeskService {
  // Singleton pattern
  static final AnyDeskService _instance = AnyDeskService._internal();
  factory AnyDeskService() => _instance;
  AnyDeskService._internal();
  
  // AnyDesk website URLs
  final String _anydeskWebsiteUrl = 'https://anydesk.com/downloads';
  final String _anydeskSecurityGuideUrl = 'https://anydesk.com/security';
  
  // Open AnyDesk website for downloads
  Future<bool> openAnyDeskDownloadPage() async {
    try {
      final Uri uri = Uri.parse(_anydeskWebsiteUrl);
      return await launchUrl(uri, mode: LaunchMode.externalApplication);
    } catch (e) {
      debugPrint('Error opening AnyDesk download page: $e');
      return false;
    }
  }
  
  // Open AnyDesk security guide
  Future<bool> openAnyDeskSecurityGuide() async {
    try {
      final Uri uri = Uri.parse(_anydeskSecurityGuideUrl);
      return await launchUrl(uri, mode: LaunchMode.externalApplication);
    } catch (e) {
      debugPrint('Error opening AnyDesk security guide: $e');
      return false;
    }
  }
  
  // Get user's AnyDesk ID from Firestore
  Future<String?> getUserAnydeskId() async {
    try {
      final User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final doc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();
            
        if (doc.exists && doc.data() != null) {
          return doc.data()!['anydesk_id'];
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user AnyDesk ID: $e');
      return null;
    }
  }
  
  // Save AnyDesk ID to Firestore
  Future<bool> saveUserAnydeskId(String anydeskId) async {
    try {
      final User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .update({
              'anydesk_id': anydeskId,
              'updated_at': FieldValue.serverTimestamp(),
            });
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error saving user AnyDesk ID: $e');
      return false;
    }
  }
  
  // Save whether the user has completed AnyDesk setup in SharedPreferences
  Future<void> saveAnyDeskSetupCompleted(bool completed) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('anydesk_setup_completed', completed);
    } catch (e) {
      debugPrint('Error saving AnyDesk setup status: $e');
    }
  }
  
  // Check if user has completed AnyDesk setup
  Future<bool> hasCompletedAnyDeskSetup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('anydesk_setup_completed') ?? false;
    } catch (e) {
      debugPrint('Error checking AnyDesk setup status: $e');
      return false;
    }
  }
} 