import 'package:flutter/foundation.dart';
import '../services/secure_storage_service.dart';

/// Utility class to manage FCM token refresh state
/// Prevents excessive token refreshes that could cause loops
class TokenManager {
  static final TokenManager _instance = TokenManager._internal();
  factory TokenManager() => _instance;
  
  TokenManager._internal();
  
  static const String _lastRefreshKey = 'last_token_refresh_timestamp';
  static const int _minRefreshInterval = 10000; // 10 seconds minimum between refreshes
  
  final _secureStorage = SecureStorageService();
  bool _isCurrentlyRefreshing = false;
  
  /// Checks if a token refresh is allowed based on timing and state
  Future<bool> canRefreshToken() async {
    if (_isCurrentlyRefreshing) {
      if (kDebugMode) {
        debugPrint('[TokenManager] Refresh already in progress');
      }
      return false;
    }
    
    try {
      final lastRefreshStr = await _secureStorage.getData(_lastRefreshKey);
      final lastRefresh = lastRefreshStr != null ? int.parse(lastRefreshStr) : 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      
      if ((now - lastRefresh) < _minRefreshInterval) {
        if (kDebugMode) {
          debugPrint('[TokenManager] Refresh attempted too soon');
        }
        return false;
      }
      
      // Update timestamp and state
      await _secureStorage.storeData(_lastRefreshKey, now.toString());
      _isCurrentlyRefreshing = true;
      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('[TokenManager] Error checking refresh state');
      }
      return false;
    }
  }
  
  /// Marks current refresh as completed
  void markRefreshComplete() {
    _isCurrentlyRefreshing = false;
    if (kDebugMode) {
      debugPrint('[TokenManager] Refresh completed');
    }
  }
  
  /// Forces the ability to refresh regardless of timing
  /// Only use this in emergency situations where a refresh is critical
  Future<void> forceRefreshAvailable() async {
    try {
      _isCurrentlyRefreshing = false;
      
      // Reset the timestamp to a time far in the past
      await _secureStorage.storeData(_lastRefreshKey, '0');
      if (kDebugMode) {
        debugPrint('[TokenManager] Refresh state reset');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('[TokenManager] Error resetting refresh state');
      }
    }
  }
} 