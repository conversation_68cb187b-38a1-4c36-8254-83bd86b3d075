import React, { useState, useEffect, useRef, useCallback } from 'react';
import { MessageCircle, Users, Info, Loader2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { ChatMessage as ChatMessageType, MessageType, SenderType, TypingIndicator } from '../../types/chat';
import { RequestModel } from '../../types/request';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';
import chatService from '../../services/chatService';
import { cn } from '../../lib/utils';

interface ChatWindowProps {
  request: RequestModel;
  onClose?: () => void;
  className?: string;
}

const ChatWindow: React.FC<ChatWindowProps> = ({
  request,
  onClose,
  className
}) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isChatActive, setIsChatActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Load initial messages and set up listeners
  useEffect(() => {
    if (!request.id || !user) return;

    const loadChat = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Check chat status
        const statusResult = await chatService.getChatStatus(request.id);
        if (statusResult.success && statusResult.data) {
          setIsChatActive(statusResult.data.active);
        }

        // Load initial messages
        const messagesResult = await chatService.getMessages(request.id);
        if (messagesResult.success && messagesResult.data) {
          setMessages(messagesResult.data);
        }

        // Set up real-time listeners
        const unsubscribeMessages = chatService.listenToMessages(
          request.id,
          (newMessages) => {
            setMessages(newMessages);
            setTimeout(scrollToBottom, 100);
          }
        );

        const unsubscribeTyping = chatService.listenToTypingIndicators(
          request.id,
          (typing) => {
            // Filter out current user from typing indicators
            const otherUsersTyping = typing.filter(t => t.userId !== user.uid);
            setTypingUsers(otherUsersTyping);
          }
        );

        // Mark messages as read
        await chatService.markMessagesAsRead(request.id, user.uid);

        return () => {
          unsubscribeMessages();
          unsubscribeTyping();
        };
      } catch (error) {
        console.error('Error loading chat:', error);
        setError('Failed to load chat');
      } finally {
        setIsLoading(false);
      }
    };

    const cleanup = loadChat();

    return () => {
      cleanup.then(cleanupFn => cleanupFn?.());
    };
  }, [request.id, user, scrollToBottom]);

  // Auto-scroll when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const handleSendMessage = async (content: string, fileUrl?: string) => {
    if (!user || !content.trim() && !fileUrl) return;

    const messageInput = {
      requestId: request.id,
      senderType: user.role === 'customer' ? SenderType.CUSTOMER : SenderType.TECHNICIAN,
      senderId: user.uid,
      messageType: fileUrl ? (fileUrl.includes('image') ? MessageType.IMAGE : MessageType.FILE) : MessageType.TEXT,
      content: content.trim(),
      fileUrl,
      isRead: false
    };

    const result = await chatService.sendMessage(messageInput);
    if (!result.success) {
      console.error('Failed to send message:', result.error);
      setError('Failed to send message');
    }
  };

  const handleTyping = async (isTyping: boolean) => {
    if (!user) return;

    await chatService.setTypingIndicator(
      request.id,
      user.uid,
      user.name,
      isTyping
    );
  };

  const handleInitializeChat = async () => {
    if (!user) return;

    setIsLoading(true);
    const result = await chatService.initializeChat(request.id, user.name);
    
    if (result.success) {
      setIsChatActive(true);
    } else {
      setError('Failed to initialize chat');
    }
    setIsLoading(false);
  };

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;

    const typingText = typingUsers.length === 1
      ? `${typingUsers[0].userName} is typing...`
      : `${typingUsers.length} people are typing...`;

    return (
      <div className="flex items-center gap-2 px-4 py-2 text-sm text-gray-500">
        <div className="flex gap-1">
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
        </div>
        <span>{typingText}</span>
      </div>
    );
  };

  const renderChatHeader = () => (
    <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      <div className="flex items-center gap-3">
        <MessageCircle className="w-5 h-5 text-blue-500" />
        <div>
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
            {request.service_name || 'Chat Support'}
          </h3>
          <p className="text-sm text-gray-500">
            Request #{request.id.slice(-8)}
            {request.technician_name && ` • ${request.technician_name}`}
          </p>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <div className={cn(
          "w-2 h-2 rounded-full",
          isChatActive ? "bg-green-500" : "bg-gray-400"
        )} />
        <span className="text-sm text-gray-500">
          {isChatActive ? 'Active' : 'Inactive'}
        </span>
      </div>
    </div>
  );

  const renderChatContent = () => {
    if (isLoading) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-2 text-gray-500">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>Loading chat...</span>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-500 mb-2">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="text-blue-500 hover:underline"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    if (!isChatActive) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md mx-auto p-6">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Chat Not Active
            </h3>
            <p className="text-gray-500 mb-4">
              This chat session hasn't been started yet. 
              {user?.role === 'technician' || user?.role === 'admin' 
                ? ' Click below to start the chat.'
                : ' Please wait for a technician to start the chat.'
              }
            </p>
            {(user?.role === 'technician' || user?.role === 'admin') && (
              <button
                onClick={handleInitializeChat}
                disabled={isLoading}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
              >
                {isLoading ? 'Starting...' : 'Start Chat'}
              </button>
            )}
          </div>
        </div>
      );
    }

    return (
      <>
        {/* Messages */}
        <div 
          ref={messagesContainerRef}
          className="flex-1 overflow-y-auto p-4 space-y-4"
        >
          {messages.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <MessageCircle className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No messages yet. Start the conversation!</p>
            </div>
          ) : (
            messages.map((message, index) => (
              <ChatMessage
                key={message.id}
                message={message}
                currentUserId={user?.uid || ''}
                senderName={
                  message.senderType === SenderType.CUSTOMER 
                    ? request.customer_name || 'Customer'
                    : message.senderType === SenderType.TECHNICIAN
                    ? request.technician_name || 'Technician'
                    : 'System'
                }
                showAvatar={true}
                showTimestamp={true}
              />
            ))
          )}
          {renderTypingIndicator()}
          <div ref={messagesEndRef} />
        </div>

        {/* Chat Input */}
        <ChatInput
          onSendMessage={handleSendMessage}
          onTyping={handleTyping}
          disabled={!isChatActive}
          requestId={request.id}
          placeholder={isChatActive ? "Type a message..." : "Chat is not active"}
        />
      </>
    );
  };

  return (
    <div className={cn("flex flex-col h-full bg-gray-50 dark:bg-gray-900", className)}>
      {renderChatHeader()}
      {renderChatContent()}
    </div>
  );
};

export default ChatWindow;
