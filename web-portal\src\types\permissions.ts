import { type UserRole } from './auth';

// Define all available permissions in the system
export enum Permission {
  // Dashboard
  VIEW_DASHBOARD = 'view_dashboard',
  VIEW_ANALYTICS = 'view_analytics',
  
  // Request Management
  VIEW_ALL_REQUESTS = 'view_all_requests',
  VIEW_OWN_REQUESTS = 'view_own_requests',
  CREATE_REQUEST = 'create_request',
  UPDATE_REQUEST = 'update_request',
  DELETE_REQUEST = 'delete_request',
  ASSIGN_REQUEST = 'assign_request',
  
  // User Management
  VIEW_USERS = 'view_users',
  CREATE_USER = 'create_user',
  UPDATE_USER = 'update_user',
  DELETE_USER = 'delete_user',
  CHANGE_USER_ROLE = 'change_user_role',
  
  // Technician Management
  VIEW_TECHNICIANS = 'view_technicians',
  MANAGE_TECHNICIAN_AVAILABILITY = 'manage_technician_availability',
  VIEW_TECHNICIAN_PERFORMANCE = 'view_technician_performance',
  
  // Service Management
  VIEW_SERVICES = 'view_services',
  CREATE_SERVICE = 'create_service',
  UPDATE_SERVICE = 'update_service',
  DELETE_SERVICE = 'delete_service',
  
  // Payment Management
  VIEW_PAYMENTS = 'view_payments',
  PROCESS_REFUND = 'process_refund',
  EXPORT_TRANSACTIONS = 'export_transactions',
  
  // Chat System
  VIEW_ALL_CHATS = 'view_all_chats',
  VIEW_OWN_CHATS = 'view_own_chats',
  SEND_MESSAGE = 'send_message',
  
  // System Settings
  VIEW_SETTINGS = 'view_settings',
  UPDATE_SETTINGS = 'update_settings',
  
  // Reports
  VIEW_REPORTS = 'view_reports',
  GENERATE_REPORTS = 'generate_reports',
  EXPORT_REPORTS = 'export_reports',
}

// Define role permissions mapping
export const rolePermissions: Record<UserRole, Permission[]> = {
  admin: [
    // Admin has all permissions
    ...Object.values(Permission),
  ],
  technician: [
    // Dashboard
    Permission.VIEW_DASHBOARD,
    
    // Requests - technicians can view and update their assigned requests
    Permission.VIEW_OWN_REQUESTS,
    Permission.UPDATE_REQUEST,
    
    // Chat - technicians can chat with customers
    Permission.VIEW_OWN_CHATS,
    Permission.SEND_MESSAGE,
    
    // Technician self-management
    Permission.MANAGE_TECHNICIAN_AVAILABILITY,
    
    // Services - view only
    Permission.VIEW_SERVICES,
  ],
  customer: [
    // Customers should not have access to the web portal
    // This is enforced at login, but included here for completeness
  ],
};

// Navigation menu item interface
export interface MenuItem {
  id: string;
  label: string;
  icon?: string;
  path: string;
  permissions: Permission[];
  children?: MenuItem[];
}

// Feature flag interface for conditional rendering
export interface Feature {
  id: string;
  name: string;
  requiredPermissions: Permission[];
  requireAll?: boolean; // If true, user must have ALL permissions. If false, ANY permission is enough
}