import { where, orderBy, type QueryConstraint } from 'firebase/firestore';
import { FirebaseService, type QueryOptions } from './firebaseService';
import { 
  type TechnicianModel, 
  type CreateTechnicianInput, 
  type UpdateTechnicianInput, 
  TechnicianStatus 
} from '../types/technician';

class TechnicianService extends FirebaseService<TechnicianModel> {
  constructor() {
    super('technicians');
  }

  // Transform raw Firebase data to TechnicianModel with defaults
  private transformTechnicianData(rawData: any): TechnicianModel {
    return {
      id: rawData.id || '',
      email: rawData.email || 'No email provided',
      name: rawData.name || 'Unknown Technician',
      photo_url: rawData.photo_url || undefined,
      phone_number: rawData.phone_number || undefined,
      specialties: Array.isArray(rawData.specialties) ? rawData.specialties : [],
      status: rawData.status || TechnicianStatus.ACTIVE,
      rating: typeof rawData.rating === 'number' ? rawData.rating : 0,
      completed_requests: typeof rawData.completed_requests === 'number' ? rawData.completed_requests : 0,
      active_requests: typeof rawData.active_requests === 'number' ? rawData.active_requests : 0,
      is_available: typeof rawData.is_available === 'boolean' ? rawData.is_available : true,
      created_at: rawData.created_at || new Date(),
      updated_at: rawData.updated_at || new Date(),
    };
  }

  // Override getAll to apply transformation
  async getAll(options?: QueryOptions): Promise<TechnicianModel[]> {
    const rawTechnicians = await super.getAll(options);
    return rawTechnicians.map(tech => this.transformTechnicianData(tech));
  }

  // Override getById to apply transformation
  async getById(id: string): Promise<TechnicianModel | null> {
    const rawTechnician = await super.getById(id);
    if (!rawTechnician) return null;
    return this.transformTechnicianData(rawTechnician);
  }

  // Override query to apply transformation
  async query(constraints: any[]): Promise<TechnicianModel[]> {
    const rawTechnicians = await super.query(constraints);
    return rawTechnicians.map(tech => this.transformTechnicianData(tech));
  }

  // Get available technicians
  async getAvailableTechnicians(options?: QueryOptions): Promise<TechnicianModel[]> {
    const constraints: QueryConstraint[] = [
      where('is_available', '==', true),
      where('status', '==', TechnicianStatus.ACTIVE),
    ];

    if (options?.orderBy) {
      constraints.push(
        orderBy(options.orderBy.field, options.orderBy.direction)
      );
    } else {
      constraints.push(orderBy('rating', 'desc'), orderBy('name', 'asc'));
    }

    return this.query(constraints);
  }

  // Get technicians by status
  async getByStatus(status: TechnicianStatus): Promise<TechnicianModel[]> {
    const constraints: QueryConstraint[] = [
      where('status', '==', status),
      orderBy('name', 'asc'),
    ];

    return this.query(constraints);
  }

  // Get technicians by specialty
  async getBySpecialty(specialty: string): Promise<TechnicianModel[]> {
    const constraints: QueryConstraint[] = [
      where('specialties', 'array-contains', specialty),
      where('is_available', '==', true),
      orderBy('rating', 'desc'),
    ];

    return this.query(constraints);
  }

  // Get top rated technicians
  async getTopRated(limit: number = 10): Promise<TechnicianModel[]> {
    const constraints: QueryConstraint[] = [
      where('is_available', '==', true),
      where('rating', '>', 0),
      orderBy('rating', 'desc'),
      orderBy('completed_requests', 'desc'),
    ];

    return this.query(constraints).then(results => results.slice(0, limit));
  }

  // Create a new technician with user account
  async createTechnician(data: CreateTechnicianInput & { password?: string }): Promise<TechnicianModel> {
    const technicianData = {
      ...data,
      status: data.status || TechnicianStatus.ACTIVE,
      is_available: data.is_available ?? true,
      rating: 0,
      completed_requests: 0,
      active_requests: 0,
    };

    // Create technician profile
    const technician = await this.create(technicianData as Omit<TechnicianModel, 'id'>);

    // TODO: Optionally create user account for login
    // This would require admin SDK or a cloud function
    // For now, admin needs to manually create user accounts
    
    return technician;
  }

  // Update technician
  async updateTechnician(id: string, data: UpdateTechnicianInput): Promise<TechnicianModel> {
    return this.update(id, data);
  }

  // Update technician status
  async updateStatus(id: string, status: TechnicianStatus): Promise<TechnicianModel> {
    const updateData: Partial<TechnicianModel> = { status };

    // If status is not active, set is_available to false
    if (status !== TechnicianStatus.ACTIVE) {
      updateData.is_available = false;
    }

    return this.update(id, updateData);
  }

  // Toggle availability
  async toggleAvailability(id: string): Promise<TechnicianModel> {
    const technician = await this.getById(id);
    if (!technician) {
      throw new Error('Technician not found');
    }

    return this.update(id, { is_available: !technician.is_available });
  }

  // Increment active requests
  async incrementActiveRequests(id: string): Promise<TechnicianModel> {
    const technician = await this.getById(id);
    if (!technician) {
      throw new Error('Technician not found');
    }

    return this.update(id, { 
      active_requests: technician.active_requests + 1,
      status: TechnicianStatus.BUSY,
    });
  }

  // Decrement active requests
  async decrementActiveRequests(id: string): Promise<TechnicianModel> {
    const technician = await this.getById(id);
    if (!technician) {
      throw new Error('Technician not found');
    }

    const newActiveRequests = Math.max(0, technician.active_requests - 1);
    const updateData: Partial<TechnicianModel> = { 
      active_requests: newActiveRequests,
    };

    // If no more active requests, set status back to active
    if (newActiveRequests === 0 && technician.status === TechnicianStatus.BUSY) {
      updateData.status = TechnicianStatus.ACTIVE;
    }

    return this.update(id, updateData);
  }

  // Complete a request (update stats)
  async completeRequest(id: string, rating?: number): Promise<TechnicianModel> {
    const technician = await this.getById(id);
    if (!technician) {
      throw new Error('Technician not found');
    }

    const newCompletedRequests = technician.completed_requests + 1;
    const updateData: Partial<TechnicianModel> = { 
      completed_requests: newCompletedRequests,
      active_requests: Math.max(0, technician.active_requests - 1),
    };

    // Update rating if provided
    if (rating !== undefined && rating > 0) {
      // Calculate new average rating
      const totalRating = technician.rating * technician.completed_requests + rating;
      updateData.rating = totalRating / newCompletedRequests;
    }

    // If no more active requests, set status back to active
    if (updateData.active_requests === 0 && technician.status === TechnicianStatus.BUSY) {
      updateData.status = TechnicianStatus.ACTIVE;
    }

    return this.update(id, updateData);
  }

  // Search technicians by name or email
  async searchTechnicians(searchTerm: string): Promise<TechnicianModel[]> {
    // Get all technicians and filter client-side
    const technicians = await this.getAll({
      orderBy: { field: 'name', direction: 'asc' }
    });
    
    const lowerSearchTerm = searchTerm.toLowerCase();
    
    return technicians.filter(tech => {
      const name = tech.name.toLowerCase();
      const email = tech.email.toLowerCase();
      
      return name.includes(lowerSearchTerm) || email.includes(lowerSearchTerm);
    });
  }

  // Get technician workload stats
  async getWorkloadStats(): Promise<{
    available: number;
    busy: number;
    offline: number;
    onLeave: number;
    totalActive: number;
  }> {
    const technicians = await this.getAll();
    
    return {
      available: technicians.filter(t => t.is_available && t.status === TechnicianStatus.ACTIVE).length,
      busy: technicians.filter(t => t.status === TechnicianStatus.BUSY).length,
      offline: technicians.filter(t => t.status === TechnicianStatus.OFFLINE).length,
      onLeave: technicians.filter(t => t.status === TechnicianStatus.ON_LEAVE).length,
      totalActive: technicians.filter(t => t.active_requests > 0).length,
    };
  }

  async createTechnicianWithId(id: string, data: CreateTechnicianInput): Promise<TechnicianModel> {
    const technicianData = {
      ...data,
      status: data.status || TechnicianStatus.ACTIVE,
      is_available: data.is_available ?? true,
      rating: 0,
      completed_requests: 0,
      active_requests: 0,
    };
    return this.createWithId(id, technicianData as Omit<TechnicianModel, 'id'>);
  }
}

// Export singleton instance
export default new TechnicianService(); 