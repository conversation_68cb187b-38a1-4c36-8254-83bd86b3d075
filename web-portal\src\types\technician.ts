import { Timestamp } from 'firebase/firestore';

export enum TechnicianStatus {
  ACTIVE = 'active',
  OFFLINE = 'offline',
  BUSY = 'busy',
  ON_LEAVE = 'onLeave',
}

export interface TechnicianModel {
  id: string;
  email: string;
  name: string;
  photo_url?: string;
  phone_number?: string;
  specialties: string[];
  status: TechnicianStatus;
  rating: number;
  completed_requests: number;
  active_requests: number;
  is_available: boolean;
  created_at: Timestamp | Date;
  updated_at?: Timestamp | Date;
}

export interface CreateTechnicianInput {
  email: string;
  name: string;
  photo_url?: string;
  phone_number?: string;
  specialties: string[];
  status?: TechnicianStatus;
  is_available?: boolean;
}

export interface UpdateTechnicianInput {
  name?: string;
  photo_url?: string;
  phone_number?: string;
  specialties?: string[];
  status?: TechnicianStatus;
  rating?: number;
  completed_requests?: number;
  active_requests?: number;
  is_available?: boolean;
} 