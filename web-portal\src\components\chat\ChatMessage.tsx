import React from 'react';
import { format } from 'date-fns';
import { Download, FileText, Image as ImageIcon, User } from 'lucide-react';
import { ChatMessage as ChatMessageType, MessageType, SenderType } from '../../types/chat';
import { cn } from '../../lib/utils';
import fileUploadService from '../../services/fileUploadService';

interface ChatMessageProps {
  message: ChatMessageType;
  currentUserId: string;
  senderName?: string;
  senderAvatar?: string;
  showAvatar?: boolean;
  showTimestamp?: boolean;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  currentUserId,
  senderName,
  senderAvatar,
  showAvatar = true,
  showTimestamp = true
}) => {
  const isOwnMessage = message.senderId === currentUserId;
  const isSystemMessage = message.senderType === SenderType.SYSTEM;

  const formatTime = (date: Date | string) => {
    const messageDate = typeof date === 'string' ? new Date(date) : date;
    return format(messageDate, 'HH:mm');
  };

  const handleFileDownload = (url: string, fileName?: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName || fileUploadService.getFileNameFromUrl(url);
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderFileContent = () => {
    if (!message.fileUrl) return null;

    const fileName = fileUploadService.getFileNameFromUrl(message.fileUrl);
    const isImage = fileUploadService.isImageUrl(message.fileUrl);

    if (isImage) {
      return (
        <div className="mt-2">
          <img
            src={message.fileUrl}
            alt={fileName}
            className="max-w-xs max-h-64 rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
            onClick={() => window.open(message.fileUrl, '_blank')}
          />
          {message.content && (
            <p className="mt-2 text-sm">{message.content}</p>
          )}
        </div>
      );
    } else {
      return (
        <div className="mt-2">
          <div 
            className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            onClick={() => handleFileDownload(message.fileUrl!, fileName)}
          >
            <FileText className="w-5 h-5 text-blue-500" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">{fileName}</p>
              <p className="text-xs text-gray-500">Click to download</p>
            </div>
            <Download className="w-4 h-4 text-gray-400" />
          </div>
          {message.content && (
            <p className="mt-2 text-sm">{message.content}</p>
          )}
        </div>
      );
    }
  };

  const renderMessageContent = () => {
    switch (message.messageType) {
      case MessageType.IMAGE:
      case MessageType.FILE:
        return renderFileContent();
      case MessageType.SYSTEM:
        return (
          <div className="text-center py-2">
            <span className="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full">
              {message.content}
            </span>
          </div>
        );
      default:
        return (
          <div className="whitespace-pre-wrap break-words">
            {message.content}
          </div>
        );
    }
  };

  if (isSystemMessage) {
    return (
      <div className="flex justify-center my-4">
        {renderMessageContent()}
      </div>
    );
  }

  return (
    <div className={cn(
      "flex gap-3 mb-4",
      isOwnMessage ? "flex-row-reverse" : "flex-row"
    )}>
      {/* Avatar */}
      {showAvatar && (
        <div className="flex-shrink-0">
          {senderAvatar ? (
            <img
              src={senderAvatar}
              alt={senderName || 'User'}
              className="w-8 h-8 rounded-full object-cover"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
              <User className="w-4 h-4 text-gray-600 dark:text-gray-300" />
            </div>
          )}
        </div>
      )}

      {/* Message Content */}
      <div className={cn(
        "flex flex-col max-w-xs lg:max-w-md",
        isOwnMessage ? "items-end" : "items-start"
      )}>
        {/* Sender Name */}
        {!isOwnMessage && senderName && (
          <span className="text-xs text-gray-500 mb-1 px-1">
            {senderName}
          </span>
        )}

        {/* Message Bubble */}
        <div className={cn(
          "px-4 py-2 rounded-2xl text-sm",
          isOwnMessage
            ? "bg-blue-500 text-white rounded-br-md"
            : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-bl-md"
        )}>
          {renderMessageContent()}
        </div>

        {/* Timestamp and Read Status */}
        {showTimestamp && (
          <div className={cn(
            "flex items-center gap-1 mt-1 px-1",
            isOwnMessage ? "flex-row-reverse" : "flex-row"
          )}>
            <span className="text-xs text-gray-400">
              {formatTime(message.createdAt)}
            </span>
            {isOwnMessage && (
              <span className="text-xs text-gray-400">
                {message.isRead ? '✓✓' : '✓'}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
