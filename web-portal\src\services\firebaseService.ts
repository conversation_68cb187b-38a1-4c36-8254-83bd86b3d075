import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  Timestamp,
  type DocumentData,
  type QueryConstraint,
  type DocumentReference,
  type DocumentSnapshot,
  type QuerySnapshot,
  setDoc,
} from 'firebase/firestore';
import { db } from '../config/firebase';

export interface PaginationOptions {
  limit?: number;
  startAfter?: DocumentSnapshot;
}

export interface QueryOptions extends PaginationOptions {
  orderBy?: { field: string; direction: 'asc' | 'desc' };
  where?: Array<{ field: string; operator: any; value: any }>;
}

export class FirebaseService<T extends DocumentData> {
  protected collectionName: string;

  constructor(collectionName: string) {
    this.collectionName = collectionName;
  }

  // Get a single document by ID
  async getById(id: string): Promise<T | null> {
    try {
      const docRef = doc(db, this.collectionName, id);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as unknown as T;
      } else {
        return null;
      }
    } catch (error) {
      console.error(`Error getting document from ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Get all documents with optional query
  async getAll(options?: QueryOptions): Promise<T[]> {
    try {
      const constraints: QueryConstraint[] = [];

      // Add where constraints
      if (options?.where) {
        options.where.forEach((w) => {
          constraints.push(where(w.field, w.operator, w.value));
        });
      }

      // Add orderBy constraint
      if (options?.orderBy) {
        constraints.push(
          orderBy(options.orderBy.field, options.orderBy.direction)
        );
      }

      // Add pagination constraints
      if (options?.limit) {
        constraints.push(limit(options.limit));
      }

      if (options?.startAfter) {
        constraints.push(startAfter(options.startAfter));
      }

      const q = query(collection(db, this.collectionName), ...constraints);
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as unknown as T[];
    } catch (error) {
      console.error(`Error getting documents from ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Create a new document
  async create(data: Omit<T, 'id'>): Promise<T> {
    try {
      const docData = {
        ...data,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp(),
      };

      const docRef = await addDoc(collection(db, this.collectionName), docData);
      const createdDoc = await this.getById(docRef.id);

      if (!createdDoc) {
        throw new Error('Failed to retrieve created document');
      }

      return createdDoc;
    } catch (error) {
      console.error(`Error creating document in ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Update a document
  async update(id: string, data: Partial<T>): Promise<T> {
    try {
      const docRef = doc(db, this.collectionName, id);
      
      // Remove id from data if present
      const { id: _, ...updateData } = data as any;
      
      await updateDoc(docRef, {
        ...updateData,
        updated_at: serverTimestamp(),
      });

      const updatedDoc = await this.getById(id);
      
      if (!updatedDoc) {
        throw new Error('Failed to retrieve updated document');
      }

      return updatedDoc;
    } catch (error) {
      console.error(`Error updating document in ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Delete a document
  async delete(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.collectionName, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error(`Error deleting document from ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Soft delete (mark as not visible)
  async softDelete(id: string): Promise<T> {
    return this.update(id, { is_visible: false } as unknown as Partial<T>);
  }

  // Query documents with custom constraints
  async query(constraints: QueryConstraint[]): Promise<T[]> {
    try {
      const q = query(collection(db, this.collectionName), ...constraints);
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as unknown as T[];
    } catch (error) {
      console.error(`Error querying ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Convert Date to Timestamp for Firestore
  protected toTimestamp(date: Date | Timestamp | undefined): Timestamp | undefined {
    if (!date) return undefined;
    if (date instanceof Timestamp) return date;
    return Timestamp.fromDate(date);
  }

  // Convert Timestamp to Date for application use
  protected toDate(timestamp: Timestamp | Date | undefined): Date | undefined {
    if (!timestamp) return undefined;
    if (timestamp instanceof Date) return timestamp;
    return timestamp.toDate();
  }

  // Create a new document with a specific ID
  async createWithId(id: string, data: Omit<T, 'id'>): Promise<T> {
    try {
      const docData = {
        ...data,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp(),
      };
      const docRef = doc(db, this.collectionName, id);
      await setDoc(docRef, docData);
      const createdDoc = await this.getById(id);
      if (!createdDoc) {
        throw new Error('Failed to retrieve created document');
      }
      return createdDoc;
    } catch (error) {
      console.error(`Error creating document with ID in ${this.collectionName}:`, error);
      throw error;
    }
  }
} 