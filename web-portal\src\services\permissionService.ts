import { type User, type UserRole } from '../types/auth';
import { Permission, rolePermissions } from '../types/permissions';

class PermissionService {
  // Get all permissions for a specific role
  getRolePermissions(role: UserRole): Permission[] {
    return rolePermissions[role] || [];
  }

  // Check if a user has a specific permission
  hasPermission(user: User | null, permission: Permission): boolean {
    if (!user) return false;
    const userPermissions = this.getRolePermissions(user.role);
    return userPermissions.includes(permission);
  }

  // Check if a user has any of the specified permissions
  hasAnyPermission(user: User | null, permissions: Permission[]): boolean {
    if (!user || permissions.length === 0) return false;
    const userPermissions = this.getRolePermissions(user.role);
    return permissions.some(permission => userPermissions.includes(permission));
  }

  // Check if a user has all of the specified permissions
  hasAllPermissions(user: User | null, permissions: Permission[]): boolean {
    if (!user || permissions.length === 0) return false;
    const userPermissions = this.getRolePermissions(user.role);
    return permissions.every(permission => userPermissions.includes(permission));
  }

  // Check if a user can perform an action based on permissions
  canPerformAction(
    user: User | null, 
    permissions: Permission[], 
    requireAll: boolean = false
  ): boolean {
    if (!user) return false;
    return requireAll 
      ? this.hasAllPermissions(user, permissions)
      : this.hasAnyPermission(user, permissions);
  }

  // Get filtered list of items based on user permissions
  filterByPermissions<T extends { permissions: Permission[] }>(
    user: User | null,
    items: T[],
    requireAll: boolean = false
  ): T[] {
    if (!user) return [];
    return items.filter(item => 
      this.canPerformAction(user, item.permissions, requireAll)
    );
  }

  // Check if a user can access a specific resource
  canAccessResource(
    user: User | null,
    resourceOwnerId?: string,
    requiredPermissions?: Permission[]
  ): boolean {
    if (!user) return false;

    // Admin can access everything
    if (user.role === 'admin') return true;

    // Check ownership
    if (resourceOwnerId && user.uid === resourceOwnerId) {
      return true;
    }

    // Check permissions
    if (requiredPermissions && requiredPermissions.length > 0) {
      return this.hasAnyPermission(user, requiredPermissions);
    }

    return false;
  }

  // Generate permission-based UI hints
  getPermissionHints(user: User | null): {
    canCreateUsers: boolean;
    canManageRequests: boolean;
    canViewReports: boolean;
    canManageServices: boolean;
    canProcessPayments: boolean;
    canViewAllChats: boolean;
    isAdmin: boolean;
    isTechnician: boolean;
  } {
    return {
      canCreateUsers: this.hasPermission(user, Permission.CREATE_USER),
      canManageRequests: this.hasPermission(user, Permission.ASSIGN_REQUEST),
      canViewReports: this.hasPermission(user, Permission.VIEW_REPORTS),
      canManageServices: this.hasPermission(user, Permission.UPDATE_SERVICE),
      canProcessPayments: this.hasPermission(user, Permission.PROCESS_REFUND),
      canViewAllChats: this.hasPermission(user, Permission.VIEW_ALL_CHATS),
      isAdmin: user?.role === 'admin',
      isTechnician: user?.role === 'technician',
    };
  }
}

export default new PermissionService(); 