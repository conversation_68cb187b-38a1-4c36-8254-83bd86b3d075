/// Luxury UI Components
/// 
/// This file exports all the luxury-styled UI components for easy importing.
/// Import this file in your screens to use all UI components with a single import.
library;

import 'package:flutter/material.dart';
import 'text_styles.dart';

// Export luxury cards
export 'luxury_card.dart';

// Export luxury buttons
export 'luxury_button.dart';

// Export luxury text fields
export 'luxury_text_field.dart';

// Export luxury sections and list items
export 'luxury_section.dart';

// Export text styles
export 'text_styles.dart';

// Also export theme for direct access to styling constants
export '../theme/app_theme.dart';

/// A collection of pre-styled UI components for consistent UI across the app
class UIKit {
  // Private constructor to prevent instantiation
  UIKit._();
  
  // Standard padding values
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  
  // Standard border radius values
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;
  
  // Standard elevation values
  static const double elevationNone = 0.0;
  static const double elevationXS = 1.0;
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  static const double elevationXL = 16.0;
  
  // Create a styled text widget with the app's font style
  static Widget text(
    BuildContext context,
    String text, {
    TextStyle Function(BuildContext, {Color? color})? styleFunction,
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) {
    final TextStyle style = styleFunction != null
        ? styleFunction(context, color: color)
        : AppTextStyles.bodyMedium(context, color: color);
    
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
  
  // Create a standard app bar with consistent styling
  static AppBar appBar(
    BuildContext context, {
    required String title,
    List<Widget>? actions,
    bool centerTitle = true,
    Widget? leading,
    PreferredSizeWidget? bottom,
    Color? backgroundColor,
    double? elevation,
    bool automaticallyImplyLeading = true,
  }) {
    final theme = Theme.of(context);
    
    return AppBar(
      title: text(
        context,
        title,
        styleFunction: AppTextStyles.headingSmall,
      ),
      centerTitle: centerTitle,
      actions: actions,
      leading: leading,
      bottom: bottom,
      backgroundColor: backgroundColor ?? theme.colorScheme.surface,
      elevation: elevation ?? 0,
      automaticallyImplyLeading: automaticallyImplyLeading,
    );
  }
  
  // Create a card with consistent styling
  static Widget card(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? backgroundColor,
    double? elevation,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    
    final cardWidget = Card(
      margin: margin ?? const EdgeInsets.all(paddingS),
      elevation: elevation ?? elevationXS,
      color: backgroundColor ?? theme.colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? BorderRadius.circular(radiusM),
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(paddingM),
        child: child,
      ),
    );
    
    return onTap != null
        ? InkWell(
            onTap: onTap,
            borderRadius: borderRadius ?? BorderRadius.circular(radiusM),
            child: cardWidget,
          )
        : cardWidget;
  }
  
  // Create a standard divider
  static Widget divider(BuildContext context, {double height = 1.0, double indent = 0.0}) {
    final theme = Theme.of(context);
    
    return Divider(
      height: height + 16.0, // Add some padding
      thickness: height,
      indent: indent,
      endIndent: indent,
      color: theme.colorScheme.outline.withOpacity(0.2),
    );
  }
  
  // Create a spacer with variable height
  static Widget spacer({double height = 16.0}) {
    return SizedBox(height: height);
  }
  
  // Create a styled button
  static Widget button(
    BuildContext context, {
    required String text,
    required VoidCallback onPressed,
    bool isPrimary = true,
    bool isLoading = false,
    IconData? icon,
    Color? backgroundColor,
    Color? textColor,
    double? width,
    double height = 48.0,
    BorderRadius? borderRadius,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // Determine colors based on button type
    final bgColor = backgroundColor ?? (isPrimary ? colorScheme.primary : Colors.transparent);
    final fgColor = textColor ?? (isPrimary ? colorScheme.onPrimary : colorScheme.primary);
    
    // Create the button content
    Widget buttonContent = isLoading
        ? SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2.0,
              valueColor: AlwaysStoppedAnimation<Color>(fgColor),
            ),
          )
        : Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon, color: fgColor, size: 18),
                SizedBox(width: paddingS),
              ],
              UIKit.text(
                context,
                text,
                styleFunction: AppTextStyles.buttonMedium,
                color: fgColor,
              ),
            ],
          );
    
    // Create the button
    return SizedBox(
      width: width,
      height: height,
      child: isPrimary
          ? ElevatedButton(
              onPressed: isLoading ? null : onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: bgColor,
                foregroundColor: fgColor,
                shape: RoundedRectangleBorder(
                  borderRadius: borderRadius ?? BorderRadius.circular(radiusS),
                ),
                padding: const EdgeInsets.symmetric(horizontal: paddingM),
              ),
              child: buttonContent,
            )
          : OutlinedButton(
              onPressed: isLoading ? null : onPressed,
              style: OutlinedButton.styleFrom(
                foregroundColor: fgColor,
                side: BorderSide(color: fgColor),
                shape: RoundedRectangleBorder(
                  borderRadius: borderRadius ?? BorderRadius.circular(radiusS),
                ),
                padding: const EdgeInsets.symmetric(horizontal: paddingM),
              ),
              child: buttonContent,
            ),
    );
  }
  
  // Create a styled chip
  static Widget chip(
    BuildContext context, {
    required String label,
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
    VoidCallback? onTap,
    VoidCallback? onDeleted,
  }) {
    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? theme.colorScheme.surfaceContainerLow;
    final fgColor = textColor ?? theme.colorScheme.onSurface;
    
    return RawChip(
      label: UIKit.text(
        context, 
        label, 
        styleFunction: AppTextStyles.labelMedium,
        color: fgColor,
      ),
      backgroundColor: bgColor,
      avatar: icon != null ? Icon(icon, size: 16, color: fgColor) : null,
      onPressed: onTap,
      onDeleted: onDeleted,
      deleteIcon: const Icon(Icons.close, size: 16),
      padding: const EdgeInsets.symmetric(horizontal: paddingXS, vertical: 0),
      visualDensity: VisualDensity.compact,
    );
  }
  
  // Create a styled error message
  static Widget errorMessage(
    BuildContext context, {
    required String message,
    VoidCallback? onRetry,
  }) {
    return Container(
      padding: const EdgeInsets.all(paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(radiusS),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          UIKit.text(
            context,
            message,
            styleFunction: AppTextStyles.bodyMedium,
            color: Theme.of(context).colorScheme.onErrorContainer,
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            SizedBox(height: paddingM),
            TextButton(
              onPressed: onRetry,
              child: UIKit.text(
                context,
                'Retry',
                styleFunction: AppTextStyles.buttonMedium,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ],
      ),
    );
  }
} 