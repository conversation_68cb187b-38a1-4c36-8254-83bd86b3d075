import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:async';
import '../models/request_model.dart';
import '../models/service_model.dart';
import 'anydesk_service.dart';
import 'notification_service.dart';
import 'chat_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

class RequestService {
  // Singleton instance
  static final RequestService _instance = RequestService._internal();
  factory RequestService() => _instance;
  RequestService._internal();

  // Firestore instance
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AnyDeskService _anydeskService = AnyDeskService();
  final NotificationService _notificationService = NotificationService();
  final ChatService _chatService = ChatService();

  // Create a new service request
  Future<RequestModel> createRequest({
    required String serviceId,
    required String serviceName,
    required String serviceDescription,
    required String customerIssue,
    required double amount,
    bool isPaid = false,
    bool isVisible = false,
    String? anydeskId,
  }) async {
    try {
      debugPrint('Creating new service request for serviceId: $serviceId');

      final User? user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get user FCM token
      final String? fcmToken = await FirebaseMessaging.instance.getToken();
      debugPrint(
        'Got FCM token for request creation: ${fcmToken != null ? '${fcmToken.substring(0, 10)}...' : 'null'}',
      );

      // Check if the user already has an active request
      final activeRequest = await hasActiveRequest();
      if (activeRequest) {
        throw Exception(
          'You already have an active request. Please complete or cancel it before creating a new one.',
        );
      }

      // Get user's AnyDesk ID if available
      final String? anydeskId = await _anydeskService.getUserAnydeskId();

      // Determine status based on payment
      final String status = isPaid ? 'pending' : 'payment_pending';

      // Create request map
      final Map<String, dynamic> requestData = {
        // Snake case fields (used by mobile app)
        'customer_id': user.uid,
        'customer_name': user.displayName ?? 'Customer',
        'customer_email': user.email ?? '',
        'service_id': serviceId,
        'service_name': serviceName,
        'service_description': serviceDescription,
        'customer_issue': customerIssue,
        'anydesk_id': anydeskId,
        'status': status,
        'amount': amount,
        'is_paid': isPaid,
        'is_visible': isVisible,
        'created_at': FieldValue.serverTimestamp(),
        'chat_active': false,
        'session_active': false,

        // Add FCM token fields
        'customer_fcm_token': fcmToken,

        // Add camelCase fields for web compatibility
        'customerId': user.uid,
        'customerName': user.displayName ?? 'Customer',
        'customerEmail': user.email ?? '',
        'serviceId': serviceId,
        'serviceName': serviceName,
        'serviceDescription': serviceDescription,
        'customerIssue': customerIssue,
        'anydeskId': anydeskId,
        'isPaid': isPaid,
        'isVisible': isVisible,
        'createdAt': FieldValue.serverTimestamp(),
        'chatActive': false,
        'sessionActive': false,

        // Add FCM token in camelCase for web compatibility
        'customerFcmToken': fcmToken,
      };

      // Add to Firestore
      final docRef = await _firestore
          .collection('service_requests')
          .add(requestData);

      // Fetch the newly created document to return as a model
      final docSnapshot = await docRef.get();
      return RequestModel.fromFirestore(docSnapshot);
    } catch (e) {
      debugPrint('Error creating service request: $e');
      throw Exception('Failed to create service request: ${e.toString()}');
    }
  }

  // Finalize request after payment is successful
  Future<RequestModel> confirmPaidRequest(String requestId) async {
    try {
      // Check if document exists first
      final docSnapshot =
          await _firestore.collection('service_requests').doc(requestId).get();

      if (!docSnapshot.exists) {
        debugPrint(
          'ERROR: Document $requestId does not exist when trying to confirm payment!',
        );
        throw Exception('Request not found after payment');
      }

      debugPrint('Confirming paid request: $requestId');

      // Update the request to make it visible to technicians
      final docRef = _firestore.collection('service_requests').doc(requestId);

      await docRef.update({
        // Snake case fields (used by mobile app)
        'is_paid': true,
        'is_visible': true,
        'status': 'pending', // Change to regular pending status
        'updated_at': FieldValue.serverTimestamp(),

        // Add camelCase fields for web compatibility
        'isPaid': true,
        'isVisible': true,
        'paymentStatus': 'completed',
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Subscribe to notifications for this request (only after payment)
      await _notificationService.subscribeToRequestNotifications(requestId);

      // Fetch the updated document
      final updatedDoc = await docRef.get();
      final updatedRequest = RequestModel.fromFirestore(updatedDoc);

      // Notify technicians of the new request
      await _notificationService.notifyTechniciansOfNewRequest(
        requestId,
        updatedRequest.serviceName,
      );

      debugPrint(
        'Request updated successfully after payment: $requestId with status: ${updatedDoc.data()?['status']}',
      );

      return updatedRequest;
    } catch (e) {
      debugPrint('Error confirming paid request: $e');
      throw Exception('Failed to confirm paid request: ${e.toString()}');
    }
  }

  // Check if a request exists
  Future<bool> checkRequestExists(String requestId) async {
    try {
      debugPrint('Checking if request exists: $requestId');

      final snapshot =
          await _firestore.collection('service_requests').doc(requestId).get();

      final exists = snapshot.exists;
      if (exists) {
        debugPrint('Request $requestId exists in service_requests collection');
      } else {
        debugPrint(
          'Request $requestId does not exist in service_requests collection',
        );
      }

      return exists;
    } catch (e) {
      debugPrint('Error checking if request exists: $e');
      return false;
    }
  }

  // Update request payment status
  Future<void> updatePaymentStatus(String requestId, bool isPaid) async {
    try {
      await _firestore.collection('service_requests').doc(requestId).update({
        'is_paid': isPaid,
        'updated_at': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating payment status: $e');
      throw Exception('Failed to update payment status: ${e.toString()}');
    }
  }

  // Get a single request by ID
  Future<RequestModel?> getRequest(
    String requestId, {
    bool forceRefresh = false,
  }) async {
    try {
      final docSnapshot =
          await _firestore.collection('service_requests').doc(requestId).get();

      if (docSnapshot.exists) {
        return RequestModel.fromFirestore(docSnapshot);
      }

      return null;
    } catch (e) {
      debugPrint('Error getting request: $e');
      throw Exception('Failed to get request: ${e.toString()}');
    }
  }

  // Get all user requests
  Stream<List<RequestModel>> getUserRequests() {
    try {
      final User? user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      return _firestore
          .collection('service_requests')
          .where('customer_id', isEqualTo: user.uid)
          .orderBy('created_at', descending: true)
          .snapshots()
          .map(
            (snapshot) =>
                snapshot.docs
                    .map((doc) => RequestModel.fromFirestore(doc))
                    .toList(),
          );
    } catch (e) {
      debugPrint('Error getting user requests: $e');
      throw Exception('Failed to get user requests: ${e.toString()}');
    }
  }

  // Get user requests (one-time fetch instead of stream)
  Future<List<RequestModel>> getUserRequestsOneTime() async {
    try {
      final User? user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot =
          await _firestore
              .collection('service_requests')
              .where('customer_id', isEqualTo: user.uid)
              .orderBy('created_at', descending: true)
              .get();

      return querySnapshot.docs
          .map((doc) => RequestModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting user requests: $e');
      throw Exception('Failed to get user requests: ${e.toString()}');
    }
  }

  // Get active request (if any)
  Future<RequestModel?> getActiveRequest() async {
    try {
      final User? user = _auth.currentUser;
      if (user == null) {
        return null;
      }

      final querySnapshot =
          await _firestore
              .collection('service_requests')
              .where('customer_id', isEqualTo: user.uid)
              .where('status', whereIn: ['approved', 'inProgress'])
              .orderBy('created_at', descending: true)
              .limit(1)
              .get();

      if (querySnapshot.docs.isNotEmpty) {
        return RequestModel.fromFirestore(querySnapshot.docs.first);
      }

      return null;
    } catch (e) {
      debugPrint('Error getting active request: $e');
      return null;
    }
  }

  // Mark a session as started (without directly launching AnyDesk)
  Future<bool> markSessionStarted(String requestId) async {
    try {
      final request = await getRequest(requestId);
      if (request == null) {
        throw Exception('Request not found');
      }

      if (request.status != RequestStatus.approved &&
          request.status != RequestStatus.inProgress) {
        throw Exception('Request is not approved');
      }

      if (!request.isPaid) {
        throw Exception('Request is not paid');
      }

      // Update request status to in progress
      await _firestore.collection('service_requests').doc(requestId).update({
        'status': 'inProgress',
        'session_active': true,
        'session_start_time': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      debugPrint('Error marking session as started: $e');
      throw Exception('Failed to mark session as started: ${e.toString()}');
    }
  }

  // End a remote session
  Future<void> endRemoteSession(String requestId) async {
    try {
      final request = await getRequest(requestId);
      if (request == null) {
        throw Exception('Request not found');
      }

      // Calculate session duration in minutes
      final sessionEndTime = DateTime.now();
      int sessionDuration = 0;

      if (request.sessionStartTime != null) {
        final difference = sessionEndTime.difference(request.sessionStartTime!);
        sessionDuration = difference.inMinutes;
      }

      // Update request status
      await _firestore.collection('service_requests').doc(requestId).update({
        'session_active': false,
        'session_end_time': FieldValue.serverTimestamp(),
        'session_duration': sessionDuration,
        'updated_at': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error ending remote session: $e');
      throw Exception('Failed to end remote session: ${e.toString()}');
    }
  }

  // Submit customer review
  Future<void> submitReview(
    String requestId,
    double rating,
    String? comment,
  ) async {
    try {
      await _firestore.collection('service_requests').doc(requestId).update({
        'customer_rated': true,
        'rating': rating,
        'review_comment': comment,
        'updated_at': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error submitting review: $e');
      throw Exception('Failed to submit review: ${e.toString()}');
    }
  }

  // Cancel a request
  Future<void> cancelRequest(String requestId) async {
    try {
      final User? user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final request = await getRequest(requestId);
      if (request == null) {
        throw Exception('Request not found');
      }

      if (request.customerId != user.uid) {
        throw Exception('Unauthorized to cancel this request');
      }

      if (request.status != RequestStatus.pending) {
        throw Exception('Only pending requests can be cancelled by the user');
      }

      await _firestore.collection('service_requests').doc(requestId).update({
        'status': 'cancelled',
        'updated_at': FieldValue.serverTimestamp(),
      });

      // Unsubscribe from notifications for this request
      await _notificationService.unsubscribeFromRequestNotifications(requestId);
    } catch (e) {
      debugPrint('Error cancelling request: $e');
      throw Exception('Failed to cancel request: ${e.toString()}');
    }
  }

  // Get all available services
  Future<List<ServiceModel>> getAvailableServices() async {
    try {
      final querySnapshot =
          await _firestore
              .collection('services')
              .where('is_active', isEqualTo: true)
              .get();

      return querySnapshot.docs
          .map((doc) => ServiceModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting available services: $e');
      throw Exception('Failed to get available services: ${e.toString()}');
    }
  }

  // Check if user has any active request (including payment pending)
  Future<bool> hasActiveRequest() async {
    try {
      final User? user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // Query for active requests (including payment_pending)
      final querySnapshot =
          await _firestore
              .collection('service_requests')
              .where('customer_id', isEqualTo: user.uid)
              .where(
                'status',
                whereIn: [
                  'payment_pending',
                  'pending',
                  'approved',
                  'inProgress',
                ],
              )
              .limit(1)
              .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking active requests: $e');
      return false; // Default to false on error
    }
  }

  // Get pending request (if any) - useful for resuming payment
  Future<RequestModel?> getPendingRequest() async {
    try {
      final User? user = _auth.currentUser;
      if (user == null) {
        return null;
      }

      final querySnapshot =
          await _firestore
              .collection('service_requests')
              .where('customer_id', isEqualTo: user.uid)
              .where('status', isEqualTo: 'payment_pending')
              .orderBy('created_at', descending: true)
              .limit(1)
              .get();

      if (querySnapshot.docs.isNotEmpty) {
        return RequestModel.fromFirestore(querySnapshot.docs.first);
      }

      return null;
    } catch (e) {
      debugPrint('Error getting pending request: $e');
      return null;
    }
  }

  // Listen for real-time updates on a specific request
  Stream<RequestModel?> watchRequest(String requestId) {
    try {
      debugPrint('Setting up watch stream for request: $requestId');
      return _firestore
          .collection('service_requests')
          .doc(requestId)
          .snapshots(
            includeMetadataChanges: true,
          ) // Include metadata changes for faster updates
          .map((snapshot) {
            if (!snapshot.exists) {
              debugPrint('Request $requestId no longer exists (deleted)');
              _handleRequestDeleted(requestId);
              return null;
            }

            final request = RequestModel.fromFirestore(snapshot);
            debugPrint('Request $requestId updated: Status=${request.status}');

            // Check for chat force closed flag - this is set by the web app when a technician closes a chat
            final data = snapshot.data() as Map<String, dynamic>;
            final bool chatForceClosed =
                data['chat_force_closed'] == true ||
                data['chatForceClosed'] == true;

            if (chatForceClosed) {
              debugPrint(
                'Detected force_closed flag for chat in request $requestId - chat was closed by technician',
              );

              // Immediately notify the user that the request is completed and chat is closed
              _notificationService.createLocalNotification(
                title: 'Request Completed',
                body:
                    'Your service request has been completed by the technician.',
                type: 'request_completed',
                requestId: requestId,
              );

              // Mark the request as completed in the local cache if needed
              if (request.status != RequestStatus.completed) {
                debugPrint(
                  'Updating local status to completed for request $requestId',
                );
                // We don't need to update Firestore as the web app already did that
                // This is just updating our local model representation
              }
            }

            // Always return the request model from Firestore
            return request;
          });
    } catch (e) {
      debugPrint('Error watching request: $e');
      return Stream.value(null);
    }
  }

  // Set up listeners to monitor status changes and trigger appropriate actions
  Future<void> setupRequestStatusListener(String requestId) async {
    try {
      final request = await getRequest(requestId);
      if (request == null) {
        debugPrint('Cannot setup listener: Request not found');
        return;
      }

      // Previous state to compare changes
      bool wasChatActive = request.chatActive;
      RequestStatus previousStatus = request.status;

      // Listen for changes
      watchRequest(requestId).listen(
        (updatedRequest) {
          if (updatedRequest == null) {
            // Request was deleted or doesn't exist anymore
            debugPrint('Request $requestId was deleted or no longer exists');
            // Consider showing a notification to the user
            _handleRequestDeleted(requestId);
            return;
          }

          // Check for status change
          if (updatedRequest.status != previousStatus) {
            _handleStatusChange(
              requestId,
              previousStatus,
              updatedRequest.status,
              updatedRequest,
            );
            previousStatus = updatedRequest.status;
          }

          // Check for chat activation
          if (!wasChatActive && updatedRequest.chatActive) {
            _handleChatActivation(requestId, updatedRequest);
            wasChatActive = true;
          }
        },
        onError: (e) {
          debugPrint('Error in request status listener: $e');
        },
      );

      debugPrint('Status listener set up for request: $requestId');
    } catch (e) {
      debugPrint('Error setting up request status listener: $e');
    }
  }

  // Handle request status changes
  void _handleStatusChange(
    String requestId,
    RequestStatus oldStatus,
    RequestStatus newStatus,
    RequestModel request,
  ) async {
    debugPrint('Request status changed: $oldStatus -> $newStatus');

    try {
      // Create notification for status change
      switch (newStatus) {
        case RequestStatus.approved:
          // Create local notification
          await _notificationService.createLocalNotification(
            title: 'Request Approved',
            body:
                'Your request has been approved by ${request.technicianName ?? 'a technician'}',
            type: 'request_accepted',
            requestId: requestId,
          );

          // Ensure chat is created when request is approved
          final chatService = ChatService();
          await chatService.initializeChat(
            requestId,
            technicianName: request.technicianName,
          );
          break;

        case RequestStatus.inProgress:
          await _notificationService.createLocalNotification(
            title: 'Session Started',
            body: 'Your technical support session is now in progress',
            type: 'session_started',
            requestId: requestId,
          );
          break;

        case RequestStatus.completed:
          await _notificationService.createLocalNotification(
            title: 'Request Completed',
            body: 'Your support request has been completed',
            type: 'request_completed',
            requestId: requestId,
          );

          // Disable chat for completed request
          final chatService = ChatService();
          await chatService.disableChatForCompletedRequest(requestId);
          break;

        default:
          // No specific notification for other status changes
          break;
      }

      // Subscribe to push notifications for this request
      await _notificationService.subscribeToRequestNotifications(requestId);

      // Force refresh the request
      await getRequest(requestId, forceRefresh: true);
    } catch (e) {
      debugPrint('Error handling status change notification: $e');
    }
  }

  // Handle chat activation
  void _handleChatActivation(String requestId, RequestModel request) async {
    try {
      // Create a notification for chat activation
      await _notificationService.createLocalNotification(
        title: 'Chat Activated',
        body:
            'Your chat with ${request.technicianName ?? 'the technician'} is now active',
        type: 'chat_activated',
        requestId: requestId,
      );

      // Attempt to set up chat service for this request
      try {
        final chatService = ChatService();
        await chatService.initializeChat(
          requestId,
          technicianName: request.technicianName,
        );
      } catch (chatError) {
        debugPrint('Error initializing chat service: $chatError');
      }
    } catch (e) {
      debugPrint('Error handling chat activation: $e');
    }
  }

  // Handle request deletion
  void _handleRequestDeleted(String requestId) {
    // You could show a notification, update UI, etc.
    debugPrint('Handling deletion of request: $requestId');

    // Create a local notification
    final notificationService = NotificationService();
    notificationService.createLocalNotification(
      title: 'Request Removed',
      body: 'Your service request has been removed by an administrator.',
      type: 'request_deleted',
    );
  }

  // Update request status - used when technician accepts or updates request
  Future<bool> updateRequestStatus(
    String requestId,
    RequestStatus status,
  ) async {
    try {
      // Map RequestStatus enum to string value
      String statusString;
      switch (status) {
        case RequestStatus.payment_pending:
          statusString = 'payment_pending';
          break;
        case RequestStatus.pending:
          statusString = 'pending';
          break;
        case RequestStatus.approved:
          statusString = 'approved';
          break;
        case RequestStatus.inProgress:
        case RequestStatus.in_progress:
          statusString = 'in-progress'; // Match web format exactly
          break;
        case RequestStatus.completed:
          statusString = 'completed';
          break;
        case RequestStatus.cancelled:
          statusString = 'cancelled';
          break;
        case RequestStatus.refused:
          statusString = 'refused';
          break;
      }

      debugPrint('Updating request $requestId status to: $statusString');

      // Update in Firestore
      await _firestore.collection('service_requests').doc(requestId).update({
        'status': statusString,
        'updated_at': FieldValue.serverTimestamp(),
        'updatedAt':
            FieldValue.serverTimestamp(), // For compatibility with web app
      });

      debugPrint(
        'Request $requestId status updated successfully to $statusString',
      );
      return true;
    } catch (e) {
      debugPrint('Error updating request status: $e');
      return false;
    }
  }

  // Accept a request as a technician
  Future<bool> acceptRequest(String requestId) async {
    try {
      final User? user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Verify the user is a technician
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) {
        throw Exception('User profile not found');
      }

      final userData = userDoc.data();
      if (userData == null || userData['role'] != 'technician') {
        throw Exception('Only technicians can accept requests');
      }

      // Get the request
      final requestDoc =
          await _firestore.collection('service_requests').doc(requestId).get();
      if (!requestDoc.exists) {
        throw Exception('Request not found');
      }

      // Update the request with technician info and status
      await _firestore.collection('service_requests').doc(requestId).update({
        'technicianId': user.uid,
        'technician_id': user.uid,
        'technicianName': user.displayName ?? 'Technician',
        'technician_name': user.displayName ?? 'Technician',
        'status': 'in-progress',
        'chat_active': true,
        'chatActive': true,
        'has_active_chat': true,
        'updated_at': FieldValue.serverTimestamp(),
      });

      // Send notification to customer that request was accepted
      try {
        await _notificationService.sendRequestAcceptedNotification(
          requestId,
          user.displayName ?? 'Technician',
        );
      } catch (notificationError) {
        debugPrint('Error sending notification: $notificationError');
        // Continue anyway since the request was accepted
      }

      // Initialize chat
      try {
        await _chatService.initializeChat(requestId);
      } catch (chatError) {
        debugPrint('Error initializing chat: $chatError');
        // Continue anyway since the request was accepted
      }

      debugPrint('Request accepted: $requestId by technician: ${user.uid}');
      return true;
    } catch (e) {
      debugPrint('Error accepting request: $e');
      throw Exception('Failed to accept request: ${e.toString()}');
    }
  }

  // Update FCM token for a specific request
  Future<bool> updateRequestFCMToken(String requestId, String? fcmToken) async {
    try {
      fcmToken ??= await FirebaseMessaging.instance.getToken();

      debugPrint(
        'Updating FCM token for request $requestId: ${fcmToken != null ? fcmToken.substring(0, fcmToken.length > 10 ? 10 : fcmToken.length) : "null"}...',
      );

      // Update the request with the token
      await _firestore.collection('service_requests').doc(requestId).update({
        'customer_fcm_token': fcmToken,
        'customerFcmToken': fcmToken,
        'updated_at': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('Successfully updated FCM token for request $requestId');
      return true;
    } catch (e) {
      debugPrint('Error updating FCM token for request $requestId: $e');
      return false;
    }
  }

  // Update FCM token when viewing a request
  Future<void> updateFCMTokenWhenViewing(String requestId) async {
    try {
      // Check if request exists
      final exists = await checkRequestExists(requestId);
      if (!exists) {
        debugPrint(
          'Cannot update FCM token: Request $requestId does not exist',
        );
        return;
      }

      // Get the current FCM token
      final fcmToken = await FirebaseMessaging.instance.getToken();
      if (fcmToken == null) {
        debugPrint('Cannot update FCM token: No FCM token available');
        return;
      }

      // Get the request document
      final doc =
          await _firestore.collection('service_requests').doc(requestId).get();
      final data = doc.data();

      // Check if FCM token is already set and matches current token
      if (data != null &&
          (data['customer_fcm_token'] == fcmToken ||
              data['customerFcmToken'] == fcmToken)) {
        // No need to update, token already matches
        debugPrint('FCM token for request $requestId already up to date');
        return;
      }

      // Update the request with the token
      debugPrint('Updating FCM token for request $requestId when viewing');
      await _firestore.collection('service_requests').doc(requestId).update({
        'customer_fcm_token': fcmToken,
        'customerFcmToken': fcmToken,
        'updated_at': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('FCM token updated successfully for request $requestId');
    } catch (e) {
      debugPrint('Error updating FCM token when viewing request: $e');
      // Non-critical error, don't rethrow
    }
  }
}
