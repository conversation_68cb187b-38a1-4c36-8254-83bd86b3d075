import 'package:flutter/material.dart';
import '../utils/font_manager.dart';

class AppTextStyles {
  // Private constructor to prevent instantiation
  AppTextStyles._();
  
  // Get standard text style with appropriate font family based on locale
  static TextStyle getBaseStyle(BuildContext context, {
    double? fontSize, 
    FontWeight? fontWeight, 
    Color? color,
    double? letterSpacing,
    double? height,
    FontStyle? fontStyle,
    bool isPrimary = false,
  }) {
    final theme = Theme.of(context);
    
    // Use primary font (Playfair Display/Tajawal) or secondary font (Montserrat/Tajawal)
    final fontFamily = isPrimary 
        ? FontManager.getPrimaryFontFamily(context) 
        : FontManager.getSecondaryFontFamily(context);
    
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color ?? theme.colorScheme.onSurface,
      letterSpacing: letterSpacing,
      height: height,
      fontStyle: fontStyle,
    );
  }
  
  // Common text styles
  
  // Display styles (for large headlines, splash screens)
  static TextStyle displayLarge(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 30,
    fontWeight: FontWeight.bold,
    color: color,
    isPrimary: true,
  );
  
  static TextStyle displayMedium(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 26,
    fontWeight: FontWeight.bold,
    color: color,
    isPrimary: true,
  );
  
  static TextStyle displaySmall(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 22,
    fontWeight: FontWeight.bold,
    color: color,
    isPrimary: true,
  );
  
  // Heading styles (for section headers)
  static TextStyle headingLarge(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: color,
    isPrimary: true,
  );
  
  static TextStyle headingMedium(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: color,
    isPrimary: true,
  );
  
  static TextStyle headingSmall(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: color,
    isPrimary: true,
  );
  
  // Body styles (for main content)
  static TextStyle bodyLarge(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: color,
  );
  
  static TextStyle bodyMedium(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: color,
  );
  
  static TextStyle bodySmall(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: color,
  );
  
  // Button styles
  static TextStyle buttonLarge(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: color,
  );
  
  static TextStyle buttonMedium(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: color,
  );
  
  static TextStyle buttonSmall(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: color,
  );
  
  // Label styles (for form fields, captions)
  static TextStyle labelLarge(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: color,
  );
  
  static TextStyle labelMedium(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: color,
  );
  
  static TextStyle labelSmall(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 10,
    fontWeight: FontWeight.w500,
    color: color,
  );
} 