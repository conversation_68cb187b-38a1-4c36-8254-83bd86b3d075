import { useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import permissionService from '../services/permissionService';
import { Permission } from '../types/permissions';

export const usePermissions = () => {
  const { user } = useAuth();

  const permissions = useMemo(() => ({
    // Single permission check
    hasPermission: (permission: Permission) => 
      permissionService.hasPermission(user, permission),
    
    // Multiple permissions check (ANY)
    hasAnyPermission: (permissions: Permission[]) => 
      permissionService.hasAnyPermission(user, permissions),
    
    // Multiple permissions check (ALL)
    hasAllPermissions: (permissions: Permission[]) => 
      permissionService.hasAllPermissions(user, permissions),
    
    // Generic action check
    canPerformAction: (permissions: Permission[], requireAll = false) => 
      permissionService.canPerformAction(user, permissions, requireAll),
    
    // Resource access check
    canAccessResource: (resourceOwnerId?: string, requiredPermissions?: Permission[]) => 
      permissionService.canAccessResource(user, resourceOwnerId, requiredPermissions),
    
    // Get all user permissions
    userPermissions: user ? permissionService.getRolePermissions(user.role) : [],
    
    // Permission hints for UI
    hints: permissionService.getPermissionHints(user),
    
    // User role shortcuts
    isAdmin: user?.role === 'admin',
    isTechnician: user?.role === 'technician',
    isAuthenticated: !!user,
  }), [user]);

  return permissions;
};

// Hook for filtering items by permissions
export const usePermissionFilter = <T extends { permissions: Permission[] }>(
  items: T[],
  requireAll = false
) => {
  const { user } = useAuth();
  
  return useMemo(() => 
    permissionService.filterByPermissions(user, items, requireAll),
    [user, items, requireAll]
  );
}; 