/**
 * Chat compatibility utilities to ensure seamless communication with mobile app
 * 
 * This file contains utilities to validate and transform chat data structures
 * to maintain compatibility between the web portal and mobile app.
 */

import { ChatMessage, MessageType, SenderType, RealtimeMessage } from '../types/chat';

/**
 * Mobile app field mappings for compatibility
 */
export const MOBILE_FIELD_MAPPINGS = {
  // Message fields
  requestId: 'request_id',
  senderType: 'sender_type', 
  senderId: 'sender_id',
  messageType: 'message_type',
  fileUrl: 'file_url',
  isRead: 'is_read',
  createdAt: 'created_at',
  
  // Chat status fields
  chatActive: 'chat_active',
  lastActivity: 'last_activity',
  
  // Request fields
  customerId: 'customer_id',
  serviceId: 'service_id',
  serviceName: 'service_name',
  technicianId: 'technician_id',
  technicianName: 'technician_name',
  sessionActive: 'session_active',
  isPaid: 'is_paid',
  updatedAt: 'updated_at'
};

/**
 * Convert web portal ChatMessage to mobile app format (RealtimeMessage)
 */
export const chatMessageToRealtimeMessage = (message: ChatMessage): RealtimeMessage => {
  return {
    id: message.id,
    request_id: message.requestId,
    sender_type: message.senderType,
    sender_id: message.senderId,
    message_type: message.messageType,
    content: message.content,
    file_url: message.fileUrl,
    is_read: message.isRead,
    created_at: typeof message.createdAt === 'string' 
      ? new Date(message.createdAt).getTime()
      : message.createdAt.getTime ? message.createdAt.getTime() : Date.now()
  };
};

/**
 * Convert mobile app format (RealtimeMessage) to web portal ChatMessage
 */
export const realtimeMessageToChatMessage = (realtimeMessage: RealtimeMessage): ChatMessage => {
  return {
    id: realtimeMessage.id,
    requestId: realtimeMessage.request_id,
    senderType: realtimeMessage.sender_type as SenderType,
    senderId: realtimeMessage.sender_id,
    messageType: realtimeMessage.message_type as MessageType,
    content: realtimeMessage.content,
    fileUrl: realtimeMessage.file_url,
    isRead: realtimeMessage.is_read,
    createdAt: new Date(realtimeMessage.created_at)
  };
};

/**
 * Validate message type compatibility
 */
export const validateMessageType = (messageType: string): boolean => {
  const validTypes = Object.values(MessageType);
  return validTypes.includes(messageType as MessageType);
};

/**
 * Validate sender type compatibility
 */
export const validateSenderType = (senderType: string): boolean => {
  const validTypes = Object.values(SenderType);
  return validTypes.includes(senderType as SenderType);
};

/**
 * Normalize message data for cross-platform compatibility
 */
export const normalizeMessageData = (data: any): Partial<ChatMessage> => {
  return {
    id: data.id || '',
    requestId: data.request_id || data.requestId || '',
    senderType: (data.sender_type || data.senderType) as SenderType,
    senderId: data.sender_id || data.senderId || '',
    messageType: (data.message_type || data.messageType) as MessageType,
    content: data.content || '',
    fileUrl: data.file_url || data.fileUrl,
    isRead: data.is_read ?? data.isRead ?? false,
    createdAt: data.created_at 
      ? new Date(typeof data.created_at === 'number' ? data.created_at : data.created_at)
      : data.createdAt 
      ? new Date(data.createdAt)
      : new Date()
  };
};

/**
 * Validate chat message structure
 */
export const validateChatMessage = (message: any): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!message.id) errors.push('Message ID is required');
  if (!message.requestId && !message.request_id) errors.push('Request ID is required');
  if (!message.senderId && !message.sender_id) errors.push('Sender ID is required');
  if (!message.content && !message.fileUrl && !message.file_url) {
    errors.push('Message must have content or file URL');
  }

  const senderType = message.senderType || message.sender_type;
  if (!validateSenderType(senderType)) {
    errors.push(`Invalid sender type: ${senderType}`);
  }

  const messageType = message.messageType || message.message_type;
  if (!validateMessageType(messageType)) {
    errors.push(`Invalid message type: ${messageType}`);
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Convert camelCase to snake_case for mobile app compatibility
 */
export const camelToSnakeCase = (obj: Record<string, any>): Record<string, any> => {
  const result: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    result[snakeKey] = value;
  }
  
  return result;
};

/**
 * Convert snake_case to camelCase for web portal compatibility
 */
export const snakeToCamelCase = (obj: Record<string, any>): Record<string, any> => {
  const result: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    result[camelKey] = value;
  }
  
  return result;
};

/**
 * Ensure message has both camelCase and snake_case fields for compatibility
 */
export const ensureCompatibleFields = (message: any): any => {
  const compatible = { ...message };
  
  // Add snake_case versions of camelCase fields
  if (message.requestId && !message.request_id) {
    compatible.request_id = message.requestId;
  }
  if (message.senderType && !message.sender_type) {
    compatible.sender_type = message.senderType;
  }
  if (message.senderId && !message.sender_id) {
    compatible.sender_id = message.senderId;
  }
  if (message.messageType && !message.message_type) {
    compatible.message_type = message.messageType;
  }
  if (message.fileUrl && !message.file_url) {
    compatible.file_url = message.fileUrl;
  }
  if (message.isRead !== undefined && message.is_read === undefined) {
    compatible.is_read = message.isRead;
  }
  if (message.createdAt && !message.created_at) {
    compatible.created_at = typeof message.createdAt === 'string' 
      ? new Date(message.createdAt).getTime()
      : message.createdAt.getTime ? message.createdAt.getTime() : Date.now();
  }
  
  // Add camelCase versions of snake_case fields
  if (message.request_id && !message.requestId) {
    compatible.requestId = message.request_id;
  }
  if (message.sender_type && !message.senderType) {
    compatible.senderType = message.sender_type;
  }
  if (message.sender_id && !message.senderId) {
    compatible.senderId = message.sender_id;
  }
  if (message.message_type && !message.messageType) {
    compatible.messageType = message.message_type;
  }
  if (message.file_url && !message.fileUrl) {
    compatible.fileUrl = message.file_url;
  }
  if (message.is_read !== undefined && message.isRead === undefined) {
    compatible.isRead = message.is_read;
  }
  if (message.created_at && !message.createdAt) {
    compatible.createdAt = new Date(typeof message.created_at === 'number' ? message.created_at : message.created_at);
  }
  
  return compatible;
};

/**
 * Test compatibility with mobile app data structures
 */
export const testMobileCompatibility = (): { success: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  try {
    // Test message conversion
    const testMessage: ChatMessage = {
      id: 'test-123',
      requestId: 'req-456',
      senderType: SenderType.TECHNICIAN,
      senderId: 'user-789',
      messageType: MessageType.TEXT,
      content: 'Test message',
      isRead: false,
      createdAt: new Date()
    };
    
    // Convert to mobile format and back
    const realtimeMessage = chatMessageToRealtimeMessage(testMessage);
    const convertedBack = realtimeMessageToChatMessage(realtimeMessage);
    
    // Validate conversion
    if (convertedBack.id !== testMessage.id) {
      errors.push('Message ID conversion failed');
    }
    if (convertedBack.requestId !== testMessage.requestId) {
      errors.push('Request ID conversion failed');
    }
    if (convertedBack.senderType !== testMessage.senderType) {
      errors.push('Sender type conversion failed');
    }
    
    // Test field compatibility
    const compatibleMessage = ensureCompatibleFields(testMessage);
    if (!compatibleMessage.request_id || !compatibleMessage.sender_type) {
      errors.push('Field compatibility conversion failed');
    }
    
  } catch (error) {
    errors.push(`Compatibility test failed: ${error}`);
  }
  
  return {
    success: errors.length === 0,
    errors
  };
};
