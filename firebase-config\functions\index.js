const admin = require('firebase-admin');
const functions = require('firebase-functions');

// Initialize Firebase Admin SDK with explicitly defined credentials
try {
  admin.initializeApp({
    credential: admin.credential.applicationDefault()
  });
  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
}

// Import modules
const notificationsModule = require('./src/notifications');

// Export all functions
exports.sendFcmNotification = notificationsModule.sendFcmNotification; 