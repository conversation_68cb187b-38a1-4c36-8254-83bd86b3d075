import 'package:cloud_firestore/cloud_firestore.dart';
import 'notification_preferences_model.dart';

class UserModel {
  final String id;
  final String email;
  final String? displayName;
  final String? phoneNumber;
  final String? photoUrl;
  final String? photoURL; // Alias for photoUrl to maintain compatibility
  final String? address;
  final String? city;
  final String? country;
  final String? anydeskId;
  final bool isComplete;
  final bool isOnboarded;
  final bool emailVerified;
  final String preferredLanguage;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final NotificationPreferencesModel notificationPreferences;

  UserModel({
    required this.id,
    required this.email,
    this.displayName,
    this.phoneNumber,
    this.photoUrl,
    this.photoURL,
    this.address,
    this.city,
    this.country,
    this.anydeskId,
    this.isComplete = false,
    this.isOnboarded = false,
    this.emailVerified = false,
    this.preferredLanguage = 'en',
    required this.createdAt,
    this.updatedAt,
    NotificationPreferencesModel? notificationPreferences,
  }) : notificationPreferences =
           notificationPreferences ?? NotificationPreferencesModel();

  // Factory constructor to create a UserModel from a Firestore document
  factory UserModel.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data() ?? {};

    return UserModel(
      id: doc.id,
      email: data['email'] ?? '',
      displayName: data['full_name'] ?? data['display_name'] ?? '',
      phoneNumber: data['phone_number'],
      photoUrl: data['photo_url'],
      photoURL: data['photo_url'],
      address: data['address'],
      city: data['city'],
      country: data['country'],
      anydeskId: data['anydesk_id'],
      isComplete: data['is_complete'] ?? false,
      isOnboarded: data['is_onboarded'] ?? false,
      emailVerified: data['email_verified'] ?? false,
      preferredLanguage: data['preferred_language'] ?? 'en',
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate(),
      notificationPreferences: NotificationPreferencesModel.fromFirestore(
        data['notification_preferences'] as Map<String, dynamic>?,
      ),
    );
  }

  // Factory method to create UserModel from a document ID and Map
  // PHASE 1: Prefer snake_case but maintain full backward compatibility
  factory UserModel.fromMap(String id, Map<String, dynamic> data) {
    try {
      // Phase 1 Migration: Prefer snake_case, fallback to camelCase for safety

      // Handle the complex photo URL field (3 variants!)
      final photoUrl =
          data['photo_url'] ?? data['photoUrl'] ?? data['photoURL'];

      // Handle display name variants
      final displayName =
          data['display_name'] ??
          data['full_name'] ??
          data['displayName'] ??
          '';

      // Log field access patterns for monitoring
      if (data['phone_number'] == null && data['phoneNumber'] != null) {
        print(
          'UserModel.fromMap: Using camelCase fallback for phone_number in user $id',
        );
      }

      return UserModel(
        id: id,
        email: data['email'] ?? '',
        // Phase 1: Prefer snake_case with safe fallbacks
        displayName: displayName,
        phoneNumber: data['phone_number'] ?? data['phoneNumber'],
        photoUrl: photoUrl,
        photoURL: photoUrl, // Keep alias for compatibility
        address: data['address'],
        city: data['city'],
        country: data['country'],
        anydeskId: data['anydesk_id'] ?? data['anydeskId'],
        isComplete: data['is_complete'] ?? data['isComplete'] ?? false,
        isOnboarded: data['is_onboarded'] ?? data['isOnboarded'] ?? false,
        emailVerified: data['email_verified'] ?? data['emailVerified'] ?? false,
        preferredLanguage:
            data['preferred_language'] ?? data['preferredLanguage'] ?? 'en',
        createdAt:
            _parseTimestamp(data['created_at'] ?? data['createdAt']) ??
            DateTime.now(),
        updatedAt: _parseTimestamp(data['updated_at'] ?? data['updatedAt']),
        notificationPreferences: NotificationPreferencesModel.fromFirestore(
          (data['notification_preferences'] ?? data['notificationPreferences'])
              as Map<String, dynamic>?,
        ),
      );
    } catch (e) {
      print('Error parsing UserModel from map: $e for ID: $id');
      // Return safe empty user instead of crashing
      return UserModel(id: id, email: '', createdAt: DateTime.now());
    }
  }

  // Helper method to safely parse timestamps
  static DateTime? _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;
    if (timestamp is Timestamp) return timestamp.toDate();
    if (timestamp is DateTime) return timestamp;
    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        print('Error parsing timestamp string: $e');
        return null;
      }
    }
    return null;
  }

  // Convert UserModel to a Map for Firestore
  // PHASE 2: Write ONLY snake_case (no more dual-write)
  Map<String, dynamic> toFirestore() {
    final Map<String, dynamic> data = {
      // Use ONLY snake_case fields - standardized format
      'email': email,
      'display_name': displayName,
      'phone_number': phoneNumber,
      'photo_url': photoUrl ?? photoURL,
      'address': address,
      'city': city,
      'country': country,
      'anydesk_id': anydeskId,
      'is_complete': isComplete,
      'is_onboarded': isOnboarded,
      'email_verified': emailVerified,
      'preferred_language': preferredLanguage,
      'created_at': createdAt,
      'updated_at': updatedAt ?? FieldValue.serverTimestamp(),
      'notification_preferences': notificationPreferences.toMap(),
    };

    return data;
  }

  // Convert UserModel to a Map with camelCase keys for consistency
  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'displayName': displayName,
      'phoneNumber': phoneNumber,
      'photoUrl': photoUrl ?? photoURL,
      'address': address,
      'city': city,
      'country': country,
      'anydeskId': anydeskId,
      'isComplete': isComplete,
      'isOnboarded': isOnboarded,
      'emailVerified': emailVerified,
      'preferredLanguage': preferredLanguage,
      'notification_preferences': notificationPreferences.toMap(),
      'updated_at': FieldValue.serverTimestamp(),
    };
  }

  // Create a copy of the user with updated fields
  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? phoneNumber,
    String? photoUrl,
    String? photoURL,
    String? address,
    String? city,
    String? country,
    String? anydeskId,
    bool? isComplete,
    bool? isOnboarded,
    bool? emailVerified,
    String? preferredLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
    NotificationPreferencesModel? notificationPreferences,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      photoUrl: photoUrl ?? this.photoUrl,
      photoURL: photoURL ?? this.photoURL,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      anydeskId: anydeskId ?? this.anydeskId,
      isComplete: isComplete ?? this.isComplete,
      isOnboarded: isOnboarded ?? this.isOnboarded,
      emailVerified: emailVerified ?? this.emailVerified,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notificationPreferences:
          notificationPreferences ?? this.notificationPreferences,
    );
  }

  // Factory method to create user from Firebase auth
  factory UserModel.fromFirebaseAuth(
    String uid,
    String email, {
    String? displayName,
    String? photoUrl,
    bool emailVerified = false,
  }) {
    return UserModel(
      id: uid,
      email: email,
      displayName: displayName,
      photoUrl: photoUrl,
      photoURL: photoUrl,
      isComplete: false,
      isOnboarded: false,
      emailVerified: emailVerified,
      createdAt: DateTime.now(),
    );
  }

  // For compatibility - get uid from id
  String get uid => id;
}
