{"hosting": {"public": "build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(jpg|jpeg|png|gif|webp|svg|ico)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "service-worker.js", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "database": {"rules": "database.rules.json"}, "functions": {"predeploy": ["npm --prefix ./functions run lint", "npm --prefix ./functions run build"], "source": "functions"}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "database": {"port": 9000}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true}}}