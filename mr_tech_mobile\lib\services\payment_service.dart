import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/request_model.dart';
import '../utils/env_config.dart';
import 'request_service.dart';
import 'technician_service.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Service for handling payments using Paymob V2 Intention API
/// Supports both test mode and Paymob V2 integration
class PaymentService {
  // Singleton instance
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  final RequestService _requestService = RequestService();
  final TechnicianService _technicianService = TechnicianService();
  final EnvConfig _envConfig = EnvConfig.instance;

  // Payment modes
  static const String MODE_DUMMY = 'dummy';
  static const String MODE_PAYMOB = 'paymob';

  // Current payment mode - change to PAYMOB when ready for production
  static const String _currentMode = MODE_PAYMOB;

  // V2 API configuration
  String? _secretKey;
  String? _publicKey;
  String? _integrationId;

  // V2 API endpoints
  static const String _baseUrl = 'https://accept.paymob.com';
  static const String _intentionEndpoint = '/v1/intention/';
  static const String _checkoutUrl = '/unifiedcheckout/';

  // Initialization status
  bool _isInitialized = false;
  bool _isInitializing = false;

  /// Initialize the payment service - minimal initialization for startup
  Future<void> initialize() async {
    if (_isInitialized || _isInitializing) return;

    // Mark as initializing to prevent multiple concurrent initializations
    _isInitializing = true;

    try {
      // Set initialized to true immediately to allow the app to continue
      _isInitialized = true;

      // Defer the actual initialization to a background task
      Future.microtask(() {
        _completeInitialization();
      });
    } catch (e) {
      debugPrint('Error in minimal PaymentService initialization: $e');
      _isInitialized =
          true; // Still mark as initialized to avoid repeated attempts
    } finally {
      _isInitializing = false;
    }
  }

  /// Complete initialization in the background
  Future<void> _completeInitialization() async {
    try {
      // Ensure environment config is initialized
      if (!_envConfig.isInitialized) {
        await _envConfig.initialize();
      }

      await _loadEnvironmentVariables();

      if (_secretKey == null) {
        debugPrint(
          'Warning: Paymob V2 API credentials not properly configured. Falling back to test mode.',
        );
      } else {
        debugPrint('PaymentService initialized in $_currentMode mode (V2 API)');
        debugPrint(
          'Secret Key: ${_secretKey != null ? "${_secretKey!.substring(0, 10)}... (${_secretKey!.length} chars)" : "Not loaded"}',
        );
        debugPrint(
          'Public Key: ${_publicKey != null ? "${_publicKey!.substring(0, 10)}... (${_publicKey!.length} chars)" : "Not loaded"}',
        );
      }
    } catch (e) {
      debugPrint('Error in background PaymentService initialization: $e');
    }
  }

  /// Public method to complete initialization - called from AppService
  Future<void> completeInitialization() async {
    return _completeInitialization();
  }

  /// Ensure the service is fully initialized before processing payments
  Future<void> ensureFullyInitialized() async {
    if (!_isInitialized || _secretKey == null) {
      debugPrint(
        'PaymentService not fully initialized, attempting to load credentials...',
      );

      // Ensure environment config is initialized first
      if (!_envConfig.isInitialized) {
        debugPrint('Environment config not initialized, loading now...');
        await _envConfig.initialize();
      }

      // Load environment variables
      await _loadEnvironmentVariables();

      // If still missing credentials after retry, throw an error
      if (_secretKey == null || _secretKey!.isEmpty) {
        debugPrint(
          '❌ PAYMOB_SECRET_KEY is still missing after environment initialization',
        );
        debugPrint('📁 Environment config status: ${_envConfig.isInitialized}');

        // Try to get the value directly from dotenv as a fallback
        try {
          await dotenv.load(fileName: '.env');
          final directSecretKey = dotenv.env['PAYMOB_SECRET_KEY'];
          final directPublicKey = dotenv.env['PAYMOB_PUBLIC_KEY'];
          final directIntegrationId = dotenv.env['PAYMOB_INTEGRATION_ID'];

          debugPrint('🔍 Direct .env file check:');
          debugPrint(
            '  PAYMOB_SECRET_KEY: ${directSecretKey != null ? "Found (${directSecretKey.length} chars)" : "Not found"}',
          );
          debugPrint(
            '  PAYMOB_PUBLIC_KEY: ${directPublicKey != null ? "Found (${directPublicKey.length} chars)" : "Not found"}',
          );
          debugPrint(
            '  PAYMOB_INTEGRATION_ID: ${directIntegrationId ?? "Not found"}',
          );

          if (directSecretKey != null && directSecretKey.isNotEmpty) {
            _secretKey = _removeQuotes(directSecretKey);
            _publicKey =
                directPublicKey != null ? _removeQuotes(directPublicKey) : null;
            _integrationId =
                directIntegrationId != null
                    ? _removeQuotes(directIntegrationId)
                    : null;
            debugPrint(
              '✅ Successfully loaded V2 API credentials directly from .env file',
            );
          }
        } catch (e) {
          debugPrint('❌ Error loading .env file directly: $e');
        }

        // Final check
        if (_secretKey == null || _secretKey!.isEmpty) {
          throw Exception(
            'Payment gateway credentials not properly configured. Please check your .env file contains PAYMOB_SECRET_KEY for V2 API.',
          );
        }
      }

      debugPrint('✅ PaymentService fully initialized with V2 API credentials');
      debugPrint(
        'Secret Key: ${_secretKey!.substring(0, 10)}... (${_secretKey!.length} chars)',
      );
    }
  }

  /// Load environment variables from EnvConfig for V2 API
  Future<void> _loadEnvironmentVariables() async {
    try {
      // Load V2 API credentials from environment
      _secretKey = await _envConfig.get('PAYMOB_SECRET_KEY');
      _publicKey = await _envConfig.get('PAYMOB_PUBLIC_KEY');
      _integrationId = await _envConfig.get('PAYMOB_INTEGRATION_ID');

      // Validate that required credentials are provided
      if (_secretKey == null || _secretKey!.isEmpty) {
        debugPrint('❌ PAYMOB_SECRET_KEY is missing from .env file');
        debugPrint(
          '💡 Get your V2 secret key from Paymob dashboard for owner ID: 131480',
        );
      }

      if (_integrationId == null || _integrationId!.isEmpty) {
        debugPrint('❌ PAYMOB_INTEGRATION_ID is missing from .env file');
        debugPrint(
          '💡 Get your integration ID from Paymob dashboard for owner ID: 131480',
        );
      }

      // Ensure we remove any quotes that might have been left in the values
      if (_secretKey != null) {
        _secretKey = _removeQuotes(_secretKey!);
      }

      if (_publicKey != null) {
        _publicKey = _removeQuotes(_publicKey!);
      }

      if (_integrationId != null) {
        _integrationId = _removeQuotes(_integrationId!);
      }
    } catch (e) {
      debugPrint('Error loading payment environment variables: $e');
    }
  }

  /// Helper method to remove quotes from values
  String _removeQuotes(String value) {
    if ((value.startsWith('"') && value.endsWith('"')) ||
        (value.startsWith("'") && value.endsWith("'"))) {
      return value.substring(1, value.length - 1);
    }
    return value;
  }

  /// Process a payment for a service request
  /// Returns true if payment was successful, false otherwise
  Future<bool> processPayment({
    required BuildContext context,
    required RequestModel request,
    required Map<String, dynamic> billingData,
    Map<String, dynamic>? serviceData,
  }) async {
    // Ensure service is fully initialized before processing payment
    await ensureFullyInitialized();

    try {
      // Check if this is a pre-created request or needs to be created after payment
      bool createAfterPayment = serviceData != null;
      String requestId = request.id;
      RequestModel currentRequest = request;

      // If not create-after-payment mode, verify request exists
      if (!createAfterPayment) {
        // 1. First check if the request still exists to avoid later errors
        if (!await _requestService.checkRequestExists(request.id)) {
          await _showDialog(
            context: context,
            title: 'Request Not Found',
            message:
                'This service request could not be found. Please create a new request.',
          );
          return false;
        }

        // Verify that the request is in payment_pending status or check that this is the active request
        final fetchedRequest = await _requestService.getRequest(request.id);
        if (fetchedRequest == null) {
          await _showDialog(
            context: context,
            title: 'Request Not Found',
            message:
                'This service request could not be found. Please create a new request.',
          );
          return false;
        }

        currentRequest = fetchedRequest;

        if (currentRequest.status != RequestStatus.pending &&
            currentRequest.status.toString() !=
                'RequestStatus.paymentPending') {
          // Show a warning but allow payment if this request is already active
          debugPrint(
            'Warning: Attempting payment for request not in payment_pending status: ${currentRequest.status}',
          );
        }
      } else {
        // For new requests, check if the user already has an active request
        final hasActiveRequest = await _requestService.hasActiveRequest();
        if (hasActiveRequest) {
          // Check if the active request is in payment_pending status
          final pendingRequest = await _requestService.getPendingRequest();

          if (pendingRequest != null) {
            // Offer to complete payment for the existing request instead
            final shouldCompletePending = await _showConfirmationDialog(
              context: context,
              title: 'Existing Request Found',
              message:
                  'You have a pending request for ${pendingRequest.serviceName}. Would you like to complete payment for this request instead?',
            );

            if (shouldCompletePending) {
              // Update the request ID and continue with payment for the existing request
              requestId = pendingRequest.id;
              currentRequest = pendingRequest;
              createAfterPayment = false;
              debugPrint(
                'Switching to complete payment for existing request: $requestId',
              );
            } else {
              // Cancel the pending request first
              try {
                await _requestService.cancelRequest(pendingRequest.id);
                debugPrint(
                  'Cancelled existing pending request: ${pendingRequest.id}',
                );
              } catch (e) {
                debugPrint('Error cancelling pending request: $e');
                // Proceed anyway
              }
            }
          } else {
            // User has active requests that are not in payment_pending status
            await _showDialog(
              context: context,
              title: 'Active Request Exists',
              message:
                  'You already have an active request. Please complete or cancel it before creating a new one.',
            );
            return false;
          }
        }
      }

      // 2. Verify technicians are available
      final techniciansAvailable =
          await _technicianService.areTechniciansAvailable();
      if (!techniciansAvailable) {
        await _showDialog(
          context: context,
          title: 'No Technicians Available',
          message:
              'Sorry, there are no technicians available at the moment. Please try again later.',
        );
        return false;
      }

      // 3. Process payment based on current mode
      bool paymentSuccess = false;
      try {
        if (_currentMode == MODE_DUMMY) {
          paymentSuccess = await _processDummyPayment(context, currentRequest);
        } else if (_currentMode == MODE_PAYMOB) {
          paymentSuccess = await _processPaymobPayment(
            context,
            currentRequest,
            billingData,
          );
        }
      } catch (paymentError) {
        debugPrint('Error in payment processor: $paymentError');
        await _showDialog(
          context: context,
          title: 'Payment Processing Error',
          message:
              'An error occurred while processing the payment. Please try again.',
        );
        return false;
      }

      // 4. If payment successful, create or update the request
      if (paymentSuccess) {
        try {
          if (createAfterPayment) {
            debugPrint('Creating request after successful payment');
            // Create the request in the database now that payment is successful
            try {
              requestId = await _createRequestAfterPayment(serviceData!);
              debugPrint('Payment completed for request ID: $requestId');
              return true;
            } catch (createError) {
              debugPrint('Error creating request after payment: $createError');

              // Check if error is due to existing active request
              if (createError.toString().contains('active request')) {
                await _showDialog(
                  context: context,
                  title: 'Error After Payment',
                  message:
                      'Your payment was processed, but you already have an active request. Please check your active requests.',
                );
                // Still return true since payment was successful
                debugPrint('Payment not completed for request ID: temp_id');
                return true;
              }
              rethrow;
            }
          } else {
            // Update existing request status
            await _requestService.confirmPaidRequest(requestId);
            debugPrint('Payment completed for request ID: $requestId');
            return true;
          }
        } catch (confirmError) {
          debugPrint('Error after payment: $confirmError');
          await _showDialog(
            context: context,
            title: 'Payment Confirmation Error',
            message:
                'Your payment was processed but we encountered an error confirming your request. Please contact support.',
          );
          return false;
        }
      }

      debugPrint('Payment not completed for request ID: $requestId');
      return paymentSuccess;
    } catch (e) {
      debugPrint('Error processing payment: $e');

      // Show error dialog
      await _showDialog(
        context: context,
        title: 'Payment Error',
        message:
            'An error occurred during payment processing. Please try again later.',
      );

      return false;
    }
  }

  /// Helper to show a dialog with custom title and message
  Future<void> _showDialog({
    required BuildContext context,
    required String title,
    required String message,
  }) async {
    await showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  /// Helper to show a confirmation dialog with custom title and message
  Future<bool> _showConfirmationDialog({
    required BuildContext context,
    required String title,
    required String message,
  }) async {
    return await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: Text(title),
                content: Text(message),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('Cancel'),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: const Text('Continue'),
                  ),
                ],
              ),
        ) ??
        false;
  }

  /// Process a dummy payment (for testing only)
  Future<bool> _processDummyPayment(
    BuildContext context,
    RequestModel request,
  ) async {
    try {
      return await showDialog<bool>(
            context: context,
            barrierDismissible: false,
            builder:
                (context) => AlertDialog(
                  title: const Text('Test Payment'),
                  content: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Service: ${request.serviceName}'),
                        const SizedBox(height: 8),
                        Text(
                          'Amount: EGP ${request.amount.toStringAsFixed(2)}',
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'This is a test payment system. In production, this would connect to Paymob.',
                          style: TextStyle(fontStyle: FontStyle.italic),
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Pay Now'),
                    ),
                  ],
                ),
          ) ??
          false;
    } catch (e) {
      debugPrint('Error in dummy payment dialog: $e');
      return false;
    }
  }

  /// Process a payment with Paymob using V2 Intention API
  Future<bool> _processPaymobPayment(
    BuildContext context,
    RequestModel request,
    Map<String, dynamic> billingData,
  ) async {
    try {
      // Ensure credentials are loaded
      if (!_isInitialized) {
        await initialize();
      }

      // Format billing data for Paymob V2 API
      final formattedBillingData = _formatBillingData(billingData);

      debugPrint('🔍 PAYMENT DEBUGGING:');
      debugPrint('  Request ID: ${request.id}');
      debugPrint('  Request Amount: ${request.amount} EGP');
      debugPrint('  Amount in cents: ${(request.amount * 100).toInt()}');
      debugPrint('  Service Name: ${request.serviceName}');
      debugPrint('  Integration ID: ${_getIntegrationId()}');

      // Step 1: Create an intention using V2 API
      debugPrint('Step 1: Creating intention with V2 API');
      final intentionPayload = {
        'amount':
            (request.amount * 100)
                .toInt(), // V2 uses 'amount' instead of 'amount_cents'
        'currency': 'EGP',
        'payment_methods':
            _getPaymentMethods(), // Multiple payment methods for better compatibility
        'items': [
          {
            'name': request.serviceName,
            'amount': (request.amount * 100).toInt(),
            'description':
                request.serviceDescription ?? 'Technical support service',
            'quantity': 1,
          },
        ],
        'billing_data': {
          'apartment': formattedBillingData['apartment'] ?? 'NA',
          'first_name': formattedBillingData['first_name'] ?? 'Customer',
          'last_name': formattedBillingData['last_name'] ?? 'User',
          'street': formattedBillingData['street'] ?? 'NA',
          'building': formattedBillingData['building'] ?? 'NA',
          'phone_number': formattedBillingData['phone_number'] ?? '01000000000',
          'city': formattedBillingData['city'] ?? 'Cairo',
          'country': formattedBillingData['country'] ?? 'Egypt',
          'email': formattedBillingData['email'] ?? '<EMAIL>',
          'floor': formattedBillingData['floor'] ?? 'NA',
          'state': formattedBillingData['state'] ?? 'Cairo',
        },
        'extras': {'request_id': request.id},
        'special_reference':
            'mr_tech_${request.id}_${DateTime.now().millisecondsSinceEpoch}',
        // Optional: Add notification and redirection URLs
        // 'notification_url': 'https://your-webhook-url.com/paymob-callback',
        // 'redirection_url': 'https://your-app.com/payment-success',
      };

      debugPrint('📦 INTENTION PAYLOAD (V2): ${jsonEncode(intentionPayload)}');

      final intentionResponse = await http.post(
        Uri.parse('$_baseUrl$_intentionEndpoint'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $_secretKey',
        },
        body: jsonEncode(intentionPayload),
      );

      if (intentionResponse.statusCode != 201 &&
          intentionResponse.statusCode != 200) {
        debugPrint(
          'V2 Intention creation failed: ${intentionResponse.statusCode} - ${intentionResponse.body}',
        );
        throw Exception('Failed to create payment intention');
      }

      final intentionData = jsonDecode(intentionResponse.body);
      final clientSecret = intentionData['client_secret'];

      if (clientSecret == null) {
        debugPrint('Intention response missing client_secret: $intentionData');
        throw Exception('Invalid intention response from payment gateway');
      }

      debugPrint('V2 Intention created successfully, client_secret received');

      // Step 2: Create payment URL using V2 unified checkout
      final checkoutUrl =
          '$_baseUrl$_checkoutUrl?publicKey=$_publicKey&clientSecret=$clientSecret';
      debugPrint('Opening V2 payment URL: $checkoutUrl');

      // Show payment WebView
      return await _showPaymentWebView(context, checkoutUrl);
    } catch (e) {
      debugPrint('Error processing Paymob payment: $e');

      await _showDialog(
        context: context,
        title: 'Payment Gateway Error',
        message:
            'Unable to connect to payment gateway. Please try again later. Error: ${e.toString()}',
      );

      return false;
    }
  }

  /// Format billing data for Paymob V2 API
  Map<String, String> _formatBillingData(Map<String, dynamic> userBillingData) {
    // V2 API format
    final formattedData = {
      'apartment': (userBillingData['apartment'] ?? 'NA').toString(),
      'email': (userBillingData['email'] ?? '<EMAIL>').toString(),
      'floor': (userBillingData['floor'] ?? 'NA').toString(),
      'first_name': (userBillingData['first_name'] ?? 'Customer').toString(),
      'street': (userBillingData['street'] ?? 'NA').toString(),
      'building': (userBillingData['building'] ?? 'NA').toString(),
      'phone_number':
          (userBillingData['phone_number'] ?? '01000000000').toString(),
      'shipping_method': 'NA',
      'postal_code': (userBillingData['postal_code'] ?? '00000').toString(),
      'city': (userBillingData['city'] ?? 'Cairo').toString(),
      'country': (userBillingData['country'] ?? 'Egypt').toString(),
      'last_name': (userBillingData['last_name'] ?? 'User').toString(),
      'state': (userBillingData['state'] ?? 'Cairo').toString(),
    };

    debugPrint('Formatted billing data (V2): $formattedData');
    return formattedData;
  }

  /// Show a WebView for Paymob payment
  Future<bool> _showPaymentWebView(
    BuildContext context,
    String paymentUrl,
  ) async {
    bool paymentSuccess = false;

    // Get screen dimensions for responsive sizing
    final screenSize = MediaQuery.of(context).size;
    final height = screenSize.height * 0.7; // 70% of screen height
    final width = screenSize.width * 0.9; // 90% of screen width

    final webViewController = WebViewController();

    webViewController
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      // Set user agent to mobile for better rendering
      ..setUserAgent(
        'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            debugPrint('WebView page started: $url');
          },
          onPageFinished: (String url) {
            debugPrint('WebView page finished: $url');
            // Inject JavaScript to adjust iframe content scale
            webViewController.runJavaScript('''
              document.querySelector('meta[name="viewport"]').setAttribute('content', 
                'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
              
              // Scale iframe content to fit webview
              var style = document.createElement('style');
              style.innerHTML = `
                body { 
                  margin: 0; 
                  padding: 0; 
                  transform: scale(0.99); 
                  transform-origin: top center;
                }
                iframe { 
                  width: 100% !important; 
                  height: 100% !important; 
                  overflow: hidden;
                }
              `;
              document.head.appendChild(style);
            ''');
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('WebView error: ${error.description}');

            // For V2 API, some errors on status page are expected and don't affect payment
            if (error.description == 'net::ERR_FAILED' &&
                error.url?.contains('payment-status') == true) {
              debugPrint(
                'ℹ️ Status page load error is common in V2 API and usually not critical',
              );
              debugPrint(
                'ℹ️ Payment result should still be captured from navigation events',
              );
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            debugPrint('WebView Navigation: ${request.url}');

            final url = request.url.toLowerCase();

            // V2 API - Check for payment status page (new format)
            if (url.contains(
              'accept.paymob.com/unifiedcheckout/payment-status/',
            )) {
              debugPrint(
                '✅ V2 Payment status page detected, checking payment token...',
              );

              // For V2 API, we need to extract and validate the payment token
              final uri = Uri.parse(request.url);
              final paymentToken = uri.queryParameters['payment_token'];

              if (paymentToken != null && paymentToken.isNotEmpty) {
                debugPrint(
                  '✅ Payment token received: ${paymentToken.substring(0, 50)}...',
                );
                // In V2, reaching the status page usually means payment completed
                // The actual success/failure should be determined by the token content
                paymentSuccess = true;
                debugPrint('✅ V2 Payment process completed successfully');
              } else {
                debugPrint('❌ No payment token found in status page');
                paymentSuccess = false;
              }

              Navigator.of(context).pop();
              return NavigationDecision.prevent;
            }

            // Legacy V1 API callback (backup)
            if (url.contains(
              'accept.paymobsolutions.com/api/acceptance/post_pay',
            )) {
              debugPrint('Legacy V1 callback detected, analyzing result...');

              // Parse the URL parameters to determine success/failure
              final uri = Uri.parse(request.url);
              final success = uri.queryParameters['success'];
              final errorOccurred = uri.queryParameters['error_occured'];
              final txnResponseCode = uri.queryParameters['txn_response_code'];
              final dataMessage = uri.queryParameters['data.message'];

              debugPrint('Payment result parameters:');
              debugPrint('  success: $success');
              debugPrint('  error_occured: $errorOccurred');
              debugPrint('  txn_response_code: $txnResponseCode');
              debugPrint('  data.message: $dataMessage');

              if (success == 'true' &&
                  errorOccurred != 'true' &&
                  txnResponseCode != 'ERROR') {
                debugPrint('✅ Payment completed successfully');
                paymentSuccess = true;
              } else {
                debugPrint('❌ Payment failed or was declined');
                debugPrint('   Reason: ${dataMessage ?? 'Unknown error'}');
                paymentSuccess = false;
              }

              Navigator.of(context).pop();
              return NavigationDecision.prevent;
            }

            // Allow all other navigation (including the initial iframe and payment forms)
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(paymentUrl));

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Payment'),
            contentPadding: EdgeInsets.zero,
            insetPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 24,
            ),
            content: Container(
              width: width,
              height: height,
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
              clipBehavior: Clip.antiAlias,
              child: WebViewWidget(controller: webViewController),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  paymentSuccess = false;
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
            ],
          ),
    );

    return paymentSuccess;
  }

  /// Create a new request after successful payment
  Future<String> _createRequestAfterPayment(
    Map<String, dynamic> serviceData,
  ) async {
    try {
      debugPrint('Creating request after successful payment');

      // Create the request with the service data
      final requestModel = await _requestService.createRequest(
        serviceId: serviceData['serviceId'],
        serviceName: serviceData['serviceName'],
        serviceDescription: serviceData['serviceDescription'],
        customerIssue: serviceData['customerIssue'],
        amount: serviceData['amount'],
        isPaid: true, // Request is already paid
        isVisible: true, // Make visible to technicians immediately
      );

      // Log the new request ID
      debugPrint(
        'Created new request with ID: ${requestModel.id} after payment',
      );

      return requestModel.id;
    } catch (e) {
      debugPrint('Error creating request after payment: $e');
      throw Exception('Failed to create request after payment: $e');
    }
  }

  /// Helper to get integration ID as an integer
  int _getIntegrationId() {
    if (_integrationId == null || _integrationId!.isEmpty) {
      throw Exception(
        '❌ PAYMOB_INTEGRATION_ID is required but missing from .env file. '
        'Please add your correct integration ID from Paymob dashboard (owner ID: 131480). '
        'The previous integration ID 3282480 belongs to a different account (owner: 231584).',
      );
    }

    try {
      final parsedId = int.parse(_integrationId!);
      debugPrint('✅ Using integration ID from .env file: $parsedId');
      return parsedId;
    } catch (e) {
      throw Exception(
        '❌ Invalid PAYMOB_INTEGRATION_ID format: $_integrationId. '
        'Please provide a valid numeric integration ID from your Paymob dashboard.',
      );
    }
  }

  /// Helper to get multiple payment methods for better compatibility
  /// This allows Paymob to choose the best payment method for the customer
  List<int> _getPaymentMethods() {
    final mainIntegrationId = _getIntegrationId();
    final paymentMethods = <int>[mainIntegrationId];

    debugPrint('🎯 Using payment methods: $paymentMethods');
    debugPrint(
      '💡 If you get "No universal account" error, check that your integration ID supports card payments',
    );

    return paymentMethods;
  }
}
