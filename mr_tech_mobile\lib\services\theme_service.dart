import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';
import '../utils/font_manager.dart';

class ThemeService extends ChangeNotifier {
  // Theme mode
  ThemeMode _themeMode = ThemeMode.system;
  
  // Get theme mode
  ThemeMode get themeMode => _themeMode;
  
  // Check if dark mode
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  
  // Constructor
  ThemeService({bool isDarkMode = false}) {
    _themeMode = isDarkMode ? ThemeMode.dark : ThemeMode.light;
    _loadThemePreference();
  }
  
  // Load theme preference from SharedPreferences
  Future<void> _loadThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // Check both keys to ensure backward compatibility
      final isDark = prefs.getBool('is_dark_mode') ?? prefs.getBool('darkMode') ?? false;
      _themeMode = isDark ? ThemeMode.dark : ThemeMode.light;
      
      // Ensure both keys are synchronized
      await prefs.setBool('is_dark_mode', isDark);
      await prefs.setBool('darkMode', isDark);
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading theme preference: $e');
    }
  }
  
  // Toggle theme
  Future<void> toggleTheme(bool isDark) async {
    _themeMode = isDark ? ThemeMode.dark : ThemeMode.light;
    
    // Save preference to SharedPreferences
    try {
      final prefs = await SharedPreferences.getInstance();
      // Save to both keys to ensure consistency
      await prefs.setBool('is_dark_mode', isDark);
      await prefs.setBool('darkMode', isDark);
    } catch (e) {
      debugPrint('Error saving theme preference: $e');
    }
    
    notifyListeners();
  }
  
  // Light theme - Use BuildContext when available
  ThemeData get lightTheme {
    // Create a default ThemeData as fallback when no context is available
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4335A7),
        brightness: Brightness.light,
      ),
      fontFamily: FontManager.defaultFontFamily,
    );
  }
  
  // Use this when you have a BuildContext
  ThemeData getLightTheme(BuildContext context) {
    return AppTheme.lightTheme(context);
  }
  
  // Dark theme - Use BuildContext when available
  ThemeData get darkTheme {
    // Create a default ThemeData as fallback when no context is available
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4335A7),
        brightness: Brightness.dark,
      ),
      fontFamily: FontManager.defaultFontFamily,
    );
  }
  
  // Use this when you have a BuildContext
  ThemeData getDarkTheme(BuildContext context) {
    return AppTheme.darkTheme(context);
  }
} 