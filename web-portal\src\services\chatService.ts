import {
  ref,
  push,
  set,
  get,
  onValue,
  off,
  serverTimestamp,
  query,
  orderByChild,
  limitToLast,
  update,
  remove
} from 'firebase/database';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDoc,
  getDocs,
  query as firestoreQuery,
  where,
  orderBy,
  limit,
  serverTimestamp as firestoreServerTimestamp
} from 'firebase/firestore';
import { realtimeDb, db } from '../config/firebase';
import {
  ChatMessage,
  ChatMessageInput,
  MessageType,
  SenderType,
  RealtimeMessage,
  RealtimeChatStatus,
  RealtimeTypingStatus,
  ChatServiceResponse,
  TypingIndicator,
  FileUploadProgress
} from '../types/chat';
import fileUploadService from './fileUploadService';
import {
  chatMessageToRealtimeMessage,
  realtimeMessageToChatMessage,
  ensureCompatibleFields,
  validateChatMessage
} from '../utils/chatCompatibility';

class ChatService {
  private messageListeners: Map<string, () => void> = new Map();
  private typingListeners: Map<string, () => void> = new Map();
  private chatStatusListeners: Map<string, () => void> = new Map();

  /**
   * Initialize chat for a request (called when technician accepts request)
   */
  async initializeChat(requestId: string, technicianName?: string): Promise<ChatServiceResponse> {
    try {
      // Check if chat is already active
      const chatStatus = await this.getChatStatus(requestId);
      if (chatStatus.data?.active) {
        return { success: true, data: { message: 'Chat already active' } };
      }

      // Create welcome system message
      const welcomeMessage = technicianName 
        ? `Chat is now active. ${technicianName} will assist you shortly.`
        : 'Chat is now active. The technician will assist you shortly.';

      const systemMessage: ChatMessageInput = {
        requestId,
        senderType: SenderType.SYSTEM,
        senderId: 'system',
        messageType: MessageType.SYSTEM,
        content: welcomeMessage,
        isRead: true
      };

      // Send system message
      await this.sendMessage(systemMessage);

      // Update chat status to active
      await this.updateChatStatus(requestId, true);

      // Update request in Firestore
      await updateDoc(doc(db, 'service_requests', requestId), {
        chat_active: true,
        updated_at: firestoreServerTimestamp()
      });

      return { success: true, data: { message: 'Chat initialized successfully' } };
    } catch (error) {
      console.error('Error initializing chat:', error);
      return { 
        success: false, 
        error: { 
          code: 'INIT_CHAT_ERROR', 
          message: 'Failed to initialize chat',
          details: error 
        } 
      };
    }
  }

  /**
   * Send a message to the chat
   */
  async sendMessage(messageInput: ChatMessageInput): Promise<ChatServiceResponse<ChatMessage>> {
    try {
      const messageId = push(ref(realtimeDb, `messages/${messageInput.requestId}`)).key;
      if (!messageId) {
        throw new Error('Failed to generate message ID');
      }

      const timestamp = Date.now();

      // Create ChatMessage first for validation
      const chatMessage: ChatMessage = {
        id: messageId,
        requestId: messageInput.requestId,
        senderType: messageInput.senderType,
        senderId: messageInput.senderId,
        messageType: messageInput.messageType,
        content: messageInput.content,
        fileUrl: messageInput.fileUrl,
        isRead: messageInput.isRead || false,
        createdAt: new Date(timestamp)
      };

      // Validate message structure
      const validation = validateChatMessage(chatMessage);
      if (!validation.valid) {
        throw new Error(`Invalid message structure: ${validation.errors.join(', ')}`);
      }

      // Convert to mobile app format for Realtime Database
      const realtimeMessage = chatMessageToRealtimeMessage(chatMessage);

      // Save to Realtime Database
      await set(ref(realtimeDb, `messages/${messageInput.requestId}/${messageId}`), realtimeMessage);

      // Also save to Firestore for persistence and web portal compatibility
      // Ensure both camelCase and snake_case fields for compatibility
      const firestoreMessage = ensureCompatibleFields({
        requestId: messageInput.requestId,
        senderType: messageInput.senderType,
        senderId: messageInput.senderId,
        messageType: messageInput.messageType,
        content: messageInput.content,
        fileUrl: messageInput.fileUrl,
        isRead: messageInput.isRead || false,
        createdAt: firestoreServerTimestamp()
      });

      await addDoc(
        collection(db, 'service_requests', messageInput.requestId, 'messages'), 
        firestoreMessage
      );

      // Update last activity
      await this.updateLastActivity(messageInput.requestId);

      return { success: true, data: chatMessage };
    } catch (error) {
      console.error('Error sending message:', error);
      return { 
        success: false, 
        error: { 
          code: 'SEND_MESSAGE_ERROR', 
          message: 'Failed to send message',
          details: error 
        } 
      };
    }
  }

  /**
   * Get chat messages for a request
   */
  async getMessages(requestId: string, limitCount: number = 50): Promise<ChatServiceResponse<ChatMessage[]>> {
    try {
      const messagesRef = ref(realtimeDb, `messages/${requestId}`);
      const messagesQuery = query(messagesRef, orderByChild('created_at'), limitToLast(limitCount));
      
      const snapshot = await get(messagesQuery);
      const messages: ChatMessage[] = [];

      if (snapshot.exists()) {
        snapshot.forEach((childSnapshot) => {
          const data = childSnapshot.val() as RealtimeMessage;
          // Use compatibility utility to convert mobile format to web format
          const chatMessage = realtimeMessageToChatMessage(data);
          messages.push(chatMessage);
        });
      }

      // Sort by creation time (oldest first)
      messages.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

      return { success: true, data: messages };
    } catch (error) {
      console.error('Error getting messages:', error);
      return { 
        success: false, 
        error: { 
          code: 'GET_MESSAGES_ERROR', 
          message: 'Failed to get messages',
          details: error 
        } 
      };
    }
  }

  /**
   * Listen for real-time message updates
   */
  listenToMessages(
    requestId: string, 
    callback: (messages: ChatMessage[]) => void,
    limitCount: number = 50
  ): () => void {
    const messagesRef = ref(realtimeDb, `messages/${requestId}`);
    const messagesQuery = query(messagesRef, orderByChild('created_at'), limitToLast(limitCount));

    const unsubscribe = onValue(messagesQuery, (snapshot) => {
      const messages: ChatMessage[] = [];

      if (snapshot.exists()) {
        snapshot.forEach((childSnapshot) => {
          const data = childSnapshot.val() as RealtimeMessage;
          // Use compatibility utility to convert mobile format to web format
          const chatMessage = realtimeMessageToChatMessage(data);
          messages.push(chatMessage);
        });
      }

      // Sort by creation time (oldest first)
      messages.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      callback(messages);
    });

    // Store the unsubscribe function
    this.messageListeners.set(requestId, unsubscribe);

    return unsubscribe;
  }

  /**
   * Stop listening to messages for a request
   */
  stopListeningToMessages(requestId: string): void {
    const unsubscribe = this.messageListeners.get(requestId);
    if (unsubscribe) {
      unsubscribe();
      this.messageListeners.delete(requestId);
    }
  }

  /**
   * Get chat status for a request
   */
  async getChatStatus(requestId: string): Promise<ChatServiceResponse<RealtimeChatStatus>> {
    try {
      const statusRef = ref(realtimeDb, `chats/${requestId}`);
      const snapshot = await get(statusRef);
      
      if (snapshot.exists()) {
        return { success: true, data: snapshot.val() as RealtimeChatStatus };
      } else {
        return { 
          success: true, 
          data: { active: false, last_activity: Date.now() } 
        };
      }
    } catch (error) {
      console.error('Error getting chat status:', error);
      return { 
        success: false, 
        error: { 
          code: 'GET_CHAT_STATUS_ERROR', 
          message: 'Failed to get chat status',
          details: error 
        } 
      };
    }
  }

  /**
   * Update chat status (active/inactive)
   */
  async updateChatStatus(requestId: string, active: boolean): Promise<ChatServiceResponse> {
    try {
      const statusData: RealtimeChatStatus = {
        active,
        last_activity: Date.now()
      };

      await set(ref(realtimeDb, `chats/${requestId}`), statusData);
      return { success: true };
    } catch (error) {
      console.error('Error updating chat status:', error);
      return { 
        success: false, 
        error: { 
          code: 'UPDATE_CHAT_STATUS_ERROR', 
          message: 'Failed to update chat status',
          details: error 
        } 
      };
    }
  }

  /**
   * Update last activity timestamp
   */
  private async updateLastActivity(requestId: string): Promise<void> {
    try {
      await update(ref(realtimeDb, `chats/${requestId}`), {
        last_activity: Date.now()
      });
    } catch (error) {
      console.error('Error updating last activity:', error);
    }
  }

  /**
   * Set typing indicator
   */
  async setTypingIndicator(
    requestId: string,
    userId: string,
    userName: string,
    isTyping: boolean
  ): Promise<ChatServiceResponse> {
    try {
      const typingData = {
        is_typing: isTyping,
        timestamp: Date.now(),
        user_name: userName
      };

      if (isTyping) {
        await set(ref(realtimeDb, `typing/${requestId}/${userId}`), typingData);
      } else {
        await remove(ref(realtimeDb, `typing/${requestId}/${userId}`));
      }

      return { success: true };
    } catch (error) {
      console.error('Error setting typing indicator:', error);
      return {
        success: false,
        error: {
          code: 'SET_TYPING_ERROR',
          message: 'Failed to set typing indicator',
          details: error
        }
      };
    }
  }

  /**
   * Listen for typing indicators
   */
  listenToTypingIndicators(
    requestId: string,
    callback: (typingUsers: TypingIndicator[]) => void
  ): () => void {
    const typingRef = ref(realtimeDb, `typing/${requestId}`);

    const unsubscribe = onValue(typingRef, (snapshot) => {
      const typingUsers: TypingIndicator[] = [];

      if (snapshot.exists()) {
        const typingData = snapshot.val() as RealtimeTypingStatus;
        Object.entries(typingData).forEach(([userId, data]) => {
          if (data.is_typing) {
            typingUsers.push({
              userId,
              userName: data.user_name,
              isTyping: data.is_typing,
              timestamp: new Date(data.timestamp)
            });
          }
        });
      }

      callback(typingUsers);
    });

    this.typingListeners.set(requestId, unsubscribe);
    return unsubscribe;
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(requestId: string, userId: string): Promise<ChatServiceResponse> {
    try {
      // Get all unread messages for this user
      const messagesRef = ref(realtimeDb, `messages/${requestId}`);
      const snapshot = await get(messagesRef);

      if (snapshot.exists()) {
        const updates: Record<string, any> = {};

        snapshot.forEach((childSnapshot) => {
          const message = childSnapshot.val() as RealtimeMessage;
          if (!message.is_read && message.sender_id !== userId) {
            updates[`messages/${requestId}/${message.id}/is_read`] = true;
          }
        });

        if (Object.keys(updates).length > 0) {
          await update(ref(realtimeDb), updates);
        }
      }

      return { success: true };
    } catch (error) {
      console.error('Error marking messages as read:', error);
      return {
        success: false,
        error: {
          code: 'MARK_READ_ERROR',
          message: 'Failed to mark messages as read',
          details: error
        }
      };
    }
  }

  /**
   * Get active chats for a user (technician or admin)
   */
  async getActiveChats(userId: string, userRole: string): Promise<ChatServiceResponse<any[]>> {
    try {
      let queryConstraints;

      if (userRole === 'technician') {
        queryConstraints = [
          where('technician_id', '==', userId),
          where('chat_active', '==', true),
          orderBy('updated_at', 'desc')
        ];
      } else if (userRole === 'admin') {
        queryConstraints = [
          where('chat_active', '==', true),
          orderBy('updated_at', 'desc')
        ];
      } else {
        // Customer
        queryConstraints = [
          where('customer_id', '==', userId),
          where('chat_active', '==', true),
          orderBy('updated_at', 'desc')
        ];
      }

      const q = firestoreQuery(collection(db, 'service_requests'), ...queryConstraints);
      const querySnapshot = await getDocs(q);

      const chats = [];
      for (const doc of querySnapshot.docs) {
        const requestData = { id: doc.id, ...doc.data() };

        // Get last message for this chat
        const lastMessageResult = await this.getMessages(doc.id, 1);
        const lastMessage = lastMessageResult.success && lastMessageResult.data?.length
          ? lastMessageResult.data[0]
          : null;

        chats.push({
          id: `chat-${doc.id}`,
          requestId: doc.id,
          request: requestData,
          lastMessage,
          unreadCount: 0, // TODO: Calculate unread count
          isOnline: false // TODO: Implement online status
        });
      }

      return { success: true, data: chats };
    } catch (error) {
      console.error('Error getting active chats:', error);
      return {
        success: false,
        error: {
          code: 'GET_ACTIVE_CHATS_ERROR',
          message: 'Failed to get active chats',
          details: error
        }
      };
    }
  }

  /**
   * Send a file message with upload
   */
  async sendFileMessage(
    file: File,
    requestId: string,
    senderId: string,
    senderType: SenderType,
    caption?: string,
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<ChatServiceResponse<ChatMessage>> {
    try {
      // Upload file first
      const uploadResult = await fileUploadService.uploadChatFile(
        file,
        requestId,
        onProgress
      );

      if (!uploadResult.success || !uploadResult.url) {
        return {
          success: false,
          error: {
            code: 'FILE_UPLOAD_ERROR',
            message: uploadResult.error || 'Failed to upload file'
          }
        };
      }

      // Determine message type based on file
      const messageType = fileUploadService.isImageUrl(uploadResult.url)
        ? MessageType.IMAGE
        : MessageType.FILE;

      // Send message with file URL
      const messageInput: ChatMessageInput = {
        requestId,
        senderType,
        senderId,
        messageType,
        content: caption || `Shared ${messageType === MessageType.IMAGE ? 'an image' : 'a file'}: ${file.name}`,
        fileUrl: uploadResult.url,
        isRead: false
      };

      return await this.sendMessage(messageInput);
    } catch (error) {
      console.error('Error sending file message:', error);
      return {
        success: false,
        error: {
          code: 'SEND_FILE_MESSAGE_ERROR',
          message: 'Failed to send file message',
          details: error
        }
      };
    }
  }

  /**
   * Send an image message with upload
   */
  async sendImageMessage(
    file: File,
    requestId: string,
    senderId: string,
    senderType: SenderType,
    caption?: string,
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<ChatServiceResponse<ChatMessage>> {
    // Validate it's an image
    if (!file.type.startsWith('image/')) {
      return {
        success: false,
        error: {
          code: 'INVALID_FILE_TYPE',
          message: 'File must be an image'
        }
      };
    }

    return this.sendFileMessage(file, requestId, senderId, senderType, caption, onProgress);
  }

  /**
   * Get chat statistics for a request
   */
  async getChatStats(requestId: string): Promise<ChatServiceResponse<any>> {
    try {
      const messagesResult = await this.getMessages(requestId, 1000); // Get more messages for stats

      if (!messagesResult.success || !messagesResult.data) {
        return { success: false, error: { code: 'GET_STATS_ERROR', message: 'Failed to get messages for stats' } };
      }

      const messages = messagesResult.data;
      const stats = {
        totalMessages: messages.length,
        totalFiles: messages.filter(m => m.messageType === MessageType.FILE || m.messageType === MessageType.IMAGE).length,
        totalImages: messages.filter(m => m.messageType === MessageType.IMAGE).length,
        participantCount: new Set(messages.map(m => m.senderId)).size,
        firstMessageAt: messages.length > 0 ? messages[0].createdAt : null,
        lastMessageAt: messages.length > 0 ? messages[messages.length - 1].createdAt : null,
        messagesPerParticipant: messages.reduce((acc, message) => {
          acc[message.senderId] = (acc[message.senderId] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      };

      return { success: true, data: stats };
    } catch (error) {
      console.error('Error getting chat stats:', error);
      return {
        success: false,
        error: {
          code: 'GET_CHAT_STATS_ERROR',
          message: 'Failed to get chat statistics',
          details: error
        }
      };
    }
  }

  /**
   * Search messages in a chat
   */
  async searchMessages(
    requestId: string,
    searchTerm: string,
    limit: number = 50
  ): Promise<ChatServiceResponse<ChatMessage[]>> {
    try {
      const messagesResult = await this.getMessages(requestId, 1000); // Get more messages to search

      if (!messagesResult.success || !messagesResult.data) {
        return { success: false, error: { code: 'SEARCH_ERROR', message: 'Failed to get messages for search' } };
      }

      const searchTermLower = searchTerm.toLowerCase();
      const filteredMessages = messagesResult.data
        .filter(message =>
          message.content.toLowerCase().includes(searchTermLower) ||
          (message.fileUrl && fileUploadService.getFileNameFromUrl(message.fileUrl).toLowerCase().includes(searchTermLower))
        )
        .slice(0, limit);

      return { success: true, data: filteredMessages };
    } catch (error) {
      console.error('Error searching messages:', error);
      return {
        success: false,
        error: {
          code: 'SEARCH_MESSAGES_ERROR',
          message: 'Failed to search messages',
          details: error
        }
      };
    }
  }

  /**
   * Clean up all listeners
   */
  cleanup(): void {
    this.messageListeners.forEach((unsubscribe) => unsubscribe());
    this.typingListeners.forEach((unsubscribe) => unsubscribe());
    this.chatStatusListeners.forEach((unsubscribe) => unsubscribe());

    this.messageListeners.clear();
    this.typingListeners.clear();
    this.chatStatusListeners.clear();
  }
}

export default new ChatService();
