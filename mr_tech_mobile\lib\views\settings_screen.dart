import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/theme_service.dart';
import '../utils/navigation_utils.dart';
import '../widgets/ui_kit.dart'; // Import luxury widgets
// Import app theme

import 'notification_settings_screen.dart';
import 'device_history_screen.dart'; // Import the new device history screen
import 'legal_document_screen.dart';
import '../utils/legal_docs_utils.dart';
import '../widgets/text_styles.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    _loadThemePreference();
  }

  Future<void> _loadThemePreference() async {
    final themeService = Provider.of<ThemeService>(context, listen: false);
    setState(() {
      _isDarkMode = themeService.isDarkMode;
    });
  }

  Future<void> _toggleTheme(bool value) async {
    final themeService = Provider.of<ThemeService>(context, listen: false);

    setState(() {
      _isDarkMode = value;
    });

    await themeService.toggleTheme(value);
  }

  void _toggleLanguage() {
    final TranslationService translationService =
        Provider.of<TranslationService>(context, listen: false);
    translationService.toggleLanguage();

    // Force rebuild of the entire app with the new locale
    setState(() {});
  }

  void _resetPassword() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.grey.shade300),
            ),
            title: Text(_translate('Reset Password')),
            content: Text(
              _translate('Password reset link will be sent to your email.'),
            ),
            actions: [
              OutlinedButton(
                onPressed: () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey.shade300),
                ),
                child: Text(_translate('Cancel')),
              ),
              FilledButton(
                onPressed: () {
                  // Send password reset email
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(_translate('Password reset email sent')),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
                style: FilledButton.styleFrom(elevation: 0),
                child: Text(_translate('Send Link')),
              ),
            ],
          ),
    );
  }

  // Helper method to get translations easily
  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(key);
  }

  // Helper methods for legal documents
  void _showPrivacyPolicy() {
    NavigationUtils.navigateWithBottomNav(
      context,
      const LegalDocumentScreen(documentType: LegalDocType.privacyPolicy),
    );
  }

  void _showTermsAndConditions() {
    NavigationUtils.navigateWithBottomNav(
      context,
      const LegalDocumentScreen(documentType: LegalDocType.termsAndConditions),
    );
  }

  void _showRefundPolicy() {
    NavigationUtils.navigateWithBottomNav(
      context,
      const LegalDocumentScreen(documentType: LegalDocType.refundPolicy),
    );
  }

  void _showServiceDeliveryPolicy() {
    NavigationUtils.navigateWithBottomNav(
      context,
      const LegalDocumentScreen(documentType: LegalDocType.shippingPolicy),
    );
  }

  void _showAboutUs() {
    NavigationUtils.navigateWithBottomNav(
      context,
      const LegalDocumentScreen(documentType: LegalDocType.aboutUs),
    );
  }

  void _showContactUs() {
    NavigationUtils.navigateWithBottomNav(
      context,
      const LegalDocumentScreen(documentType: LegalDocType.contactUs),
    );
  }

  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(context);
    final isRtl = translationService.isRtl;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Floating header
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            leading: BackButton(color: colorScheme.onSurface),
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 56), // Space for back button
                      Expanded(
                        child: Text(
                          _translate('Settings'),
                          style: AppTextStyles.headingMedium(
                            context,
                            color: colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 56), // Balance the layout
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Main content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              child: Column(
                children: [
                  // Preferences section
                  _buildSectionHeader(
                    _translate('Preferences'),
                    Icons.tune_rounded,
                  ),

                  _buildSettingsTile(
                    icon: Icons.language_rounded,
                    title: _translate('Language'),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Text(
                        isRtl ? 'AR' : 'EN',
                        style: AppTextStyles.buttonSmall(
                          context,
                          color: colorScheme.primary,
                        ),
                      ),
                    ),
                    onTap: () => _toggleLanguage(),
                  ),

                  _buildSettingsTile(
                    icon: Icons.dark_mode_rounded,
                    title: _translate('Dark Mode'),
                    trailing: Switch(
                      value: _isDarkMode,
                      onChanged: _toggleTheme,
                    ),
                    onTap:
                        () {}, // Empty callback since switch handles the interaction
                  ),

                  _buildSettingsTile(
                    icon: Icons.notifications_outlined,
                    title: _translate('Notifications'),
                    trailing: Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => const NotificationSettingsScreen(),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // Security section
                  _buildSectionHeader(
                    _translate('Security'),
                    Icons.security_rounded,
                  ),

                  _buildSettingsTile(
                    icon: Icons.devices_rounded,
                    title: _translate('Device History'),
                    subtitle: _translate('Manage connected devices'),
                    trailing: Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    onTap: () {
                      NavigationUtils.navigateKeepingStack(
                        context,
                        const DeviceHistoryScreen(),
                      );
                    },
                  ),

                  _buildSettingsTile(
                    icon: Icons.lock_reset_rounded,
                    title: _translate('Reset Password'),
                    trailing: Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    onTap: _resetPassword,
                  ),

                  const SizedBox(height: 24),

                  // Support & Help section
                  _buildSectionHeader(
                    _translate('Support & Help'),
                    Icons.help_outline_rounded,
                  ),

                  _buildSettingsTile(
                    icon: Icons.info_outline_rounded,
                    title: _translate('About Us'),
                    subtitle: _translate('Learn more about Mr.Tech'),
                    trailing: Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    onTap: _showAboutUs,
                  ),

                  _buildSettingsTile(
                    icon: Icons.contact_support_outlined,
                    title: _translate('Contact Us'),
                    subtitle: _translate('Get in touch with our support team'),
                    trailing: Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    onTap: _showContactUs,
                  ),

                  const SizedBox(height: 24),

                  // Legal section
                  _buildSectionHeader(_translate('Legal'), Icons.gavel_rounded),

                  _buildSettingsTile(
                    icon: Icons.privacy_tip_rounded,
                    title: _translate('Privacy Policy'),
                    trailing: Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    onTap: _showPrivacyPolicy,
                  ),

                  _buildSettingsTile(
                    icon: Icons.description_rounded,
                    title: _translate('Terms & Conditions'),
                    trailing: Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    onTap: _showTermsAndConditions,
                  ),

                  _buildSettingsTile(
                    icon: Icons.assignment_return_rounded,
                    title: _translate('Refund Policy'),
                    trailing: Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    onTap: _showRefundPolicy,
                  ),

                  _buildSettingsTile(
                    icon: Icons.local_shipping_outlined,
                    title: _translate('Service Delivery Policy'),
                    trailing: Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    onTap: _showServiceDeliveryPolicy,
                  ),

                  const SizedBox(height: 32),

                  // App version
                  Center(
                    child: Text(
                      'v1.0.0',
                      style: AppTextStyles.bodySmall(
                        context,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),

                  const SizedBox(height: 100), // Space for safe area
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: colorScheme.primary),
          const SizedBox(width: 8),
          Text(
            title,
            style: AppTextStyles.headingSmall(
              context,
              color: colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    String? subtitle,
    required Widget trailing,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 0,
      margin: const EdgeInsets.symmetric(vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      color: colorScheme.surface,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            children: [
              Icon(icon, size: 22, color: colorScheme.primary),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.bodyLarge(
                        context,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: AppTextStyles.bodySmall(
                          context,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              trailing,
            ],
          ),
        ),
      ),
    );
  }
}
