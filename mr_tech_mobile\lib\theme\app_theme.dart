import 'package:flutter/material.dart';
import '../utils/font_manager.dart';

class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();
  
  // Color palette - Updated with new brand colors
  static const Color _primaryColor = Color(0xFF4335A7);  // Deep purple
  static const Color _secondaryColor = Color(0xFF80C4E9); // Light blue
  static const Color _tertiaryColor = Color(0xFFFF7F3E);  // Orange
  static const Color _accentColor = Color(0xFFFF7F3E);   // Orange (same as tertiary)
  
  static const Color _successColor = Color(0xFF2E7D32);  // Emerald green
  static const Color _warningColor = Color(0xFFE65100);  // Deep amber
  static const Color _errorColor = Color(0xFFC62828);    // Ruby red
  static const Color _infoColor = Color(0xFF0277BD);     // Deep blue
  
  // Neutrals - Updated with warm undertones to match new palette
  static const Color _surfaceColor = Color(0xFFFFFFFF);  // White (was warm cream)
  static const Color _backgroundLightColor = Color(0xFFFFFFFF); // White (was warm cream)
  static const Color _backgroundDarkColor = Color(0xFF121212); // Rich black
  
  static const Color _textPrimaryLightColor = Color(0xFF1A1A1A); // Near black
  static const Color _textSecondaryLightColor = Color(0xFF4D4D4D); // Dark gray
  static const Color _textTertiaryLightColor = Color(0xFF666666); // Medium gray
  
  static const Color _textPrimaryDarkColor = Color(0xFFF5F5F5); // Warm white
  static const Color _textSecondaryDarkColor = Color(0xFFCCCCCC); // Light gray
  static const Color _textTertiaryDarkColor = Color(0xFF999999); // Medium gray
  
  // Gradients for premium feel - Updated with new colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [_primaryColor, Color(0xFF352A85)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [_accentColor, Color(0xFFE86A2D)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [_successColor, Color(0xFF1B5E20)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Shadows for depth - Enhanced for more dramatic effect
  static List<BoxShadow> get subtleShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.05),
      blurRadius: 12,
      offset: const Offset(0, 2),
      spreadRadius: 0,
    ),
  ];
  
  static List<BoxShadow> get mediumShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.08),
      blurRadius: 20,
      offset: const Offset(0, 5),
      spreadRadius: -2,
    ),
    BoxShadow(
      color: Colors.black.withOpacity(0.03),
      blurRadius: 4,
      offset: const Offset(0, 1),
      spreadRadius: 0,
    ),
  ];
  
  static List<BoxShadow> get strongShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.15),
      blurRadius: 30,
      offset: const Offset(0, 10),
      spreadRadius: -5,
    ),
    BoxShadow(
      color: Colors.black.withOpacity(0.07),
      blurRadius: 8,
      offset: const Offset(0, 3),
      spreadRadius: -1,
    ),
  ];
  
  // Rounded corners - Refined for elegance
  static const BorderRadius smallRadius = BorderRadius.all(Radius.circular(6));
  static const BorderRadius mediumRadius = BorderRadius.all(Radius.circular(12));
  static const BorderRadius largeRadius = BorderRadius.all(Radius.circular(18));
  static const BorderRadius extraLargeRadius = BorderRadius.all(Radius.circular(24));
  static const BorderRadius circularRadius = BorderRadius.all(Radius.circular(100));
  
  // Animation durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 350);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Spacing scale
  static const double spaceXS = 4.0;
  static const double spaceS = 8.0;
  static const double spaceM = 16.0;
  static const double spaceL = 24.0;
  static const double spaceXL = 32.0;
  static const double space2XL = 48.0;
  static const double space3XL = 64.0;
  
  // Generate ThemeData for light theme
  static ThemeData lightTheme(BuildContext context) {
    // Create the color scheme for light theme
    final colorScheme = ColorScheme.light(
      primary: _primaryColor,
      secondary: _secondaryColor,
      tertiary: _tertiaryColor,
      surface: _surfaceColor,
      error: _errorColor,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: Colors.white,
      onSurface: _textPrimaryLightColor,
      onError: Colors.white,
    );
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: _primaryColor,
      scaffoldBackgroundColor: _surfaceColor,
      canvasColor: _backgroundLightColor,
      fontFamily: FontManager.getSecondaryFontFamily(context),
      colorScheme: colorScheme,
      
      // Text theme with sophisticated typography
      textTheme: FontManager.createTextTheme(context, colorScheme),
      
      // Card theme - Enhanced with subtle border and refined styling
      cardTheme: CardThemeData(
        elevation: 0,
        color: _backgroundLightColor,
        shape: RoundedRectangleBorder(
          borderRadius: mediumRadius,
          side: BorderSide(
            color: _textTertiaryLightColor.withOpacity(0.1),
            width: 1,
          ),
        ),
        clipBehavior: Clip.antiAlias,
        margin: const EdgeInsets.all(spaceS),
        shadowColor: Colors.black.withOpacity(0.06),
      ),
      
      // App bar theme - Modernized with more elegant typography
      appBarTheme: AppBarTheme(
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: false,
        titleTextStyle: FontManager.appBarTitleStyle(context, _textPrimaryLightColor),
        iconTheme: IconThemeData(color: _textPrimaryLightColor),
        toolbarHeight: 64,
      ),
      
      // Button themes - Refined with more sophisticated shapes and shadows
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: _primaryColor,
          foregroundColor: Colors.white,
          textStyle: FontManager.buttonTextStyle(context, Colors.white),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: mediumRadius),
          minimumSize: const Size(0, 52),
          shadowColor: _primaryColor.withOpacity(0.3),
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: _primaryColor,
          textStyle: FontManager.buttonTextStyle(context, _primaryColor),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: mediumRadius),
          side: BorderSide(color: _primaryColor.withOpacity(0.6), width: 1.5),
          minimumSize: const Size(0, 52),
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: _primaryColor,
          textStyle: FontManager.buttonTextStyle(context, _primaryColor),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          shape: RoundedRectangleBorder(borderRadius: mediumRadius),
          minimumSize: const Size(0, 40),
        ),
      ),
      
      // Input decoration theme - Enhanced with more sophisticated styling
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: _backgroundLightColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
        border: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _textTertiaryLightColor.withOpacity(0.3), width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _textTertiaryLightColor.withOpacity(0.3), width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _primaryColor, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _errorColor, width: 1),
        ),
        labelStyle: FontManager.inputTextStyle(context, _textSecondaryLightColor),
        hintStyle: FontManager.inputTextStyle(context, _textTertiaryLightColor),
        floatingLabelStyle: TextStyle(
          fontFamily: FontManager.getSecondaryFontFamily(context),
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: _primaryColor,
        ),
      ),
      
      // Bottom navigation bar theme - Enhanced for more polished appearance
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: _backgroundLightColor,
        selectedItemColor: _primaryColor,
        unselectedItemColor: _textTertiaryLightColor,
        selectedLabelStyle: FontManager.navigationTextStyle(context, _primaryColor, selected: true),
        unselectedLabelStyle: FontManager.navigationTextStyle(context, _textTertiaryLightColor),
        elevation: 8,
        type: BottomNavigationBarType.fixed,
      ),
      
      // Dialog theme
      dialogTheme: DialogThemeData(
        backgroundColor: _backgroundLightColor,
        shape: RoundedRectangleBorder(borderRadius: largeRadius),
        elevation: 16,
        titleTextStyle: TextStyle(
          fontFamily: FontManager.getPrimaryFontFamily(context),
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: _textPrimaryLightColor,
          letterSpacing: -0.25,
        ),
        contentTextStyle: TextStyle(
          fontFamily: FontManager.getSecondaryFontFamily(context),
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: _textPrimaryLightColor,
          letterSpacing: 0.15,
          height: 1.5,
        ),
      ),
    );
  }
  
  // Generate ThemeData for dark theme
  static ThemeData darkTheme(BuildContext context) {
    // Create the color scheme for dark theme
    final colorScheme = ColorScheme.dark(
      primary: _primaryColor,
      secondary: _secondaryColor,
      tertiary: _tertiaryColor,
      surface: Color(0xFF1E293B),
      error: _errorColor,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: Colors.white,
      onSurface: _textPrimaryDarkColor,
      onError: Colors.white,
    );
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: _primaryColor,
      scaffoldBackgroundColor: _backgroundDarkColor,
      canvasColor: Color(0xFF1E293B),
      fontFamily: FontManager.getSecondaryFontFamily(context),
      colorScheme: colorScheme,
      
      // Text themes with sophisticated typography for dark mode
      textTheme: FontManager.createTextTheme(context, colorScheme),
      
      // App bar theme
      appBarTheme: AppBarTheme(
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: true,
        titleTextStyle: FontManager.appBarTitleStyle(context, _textPrimaryDarkColor),
        iconTheme: IconThemeData(color: _textPrimaryDarkColor),
        toolbarHeight: 64,
      ),
      
      // Bottom navigation bar theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: Color(0xFF1E293B).withOpacity(0.9),
        selectedItemColor: _primaryColor,
        unselectedItemColor: _textTertiaryDarkColor,
        selectedLabelStyle: FontManager.navigationTextStyle(context, _primaryColor, selected: true),
        unselectedLabelStyle: FontManager.navigationTextStyle(context, _textTertiaryDarkColor),
        elevation: 8,
        type: BottomNavigationBarType.fixed,
      ),
      
      // Other dark theme styles similar to light theme but with appropriate color adjustments
    );
  }
  
  // Helper methods for consistent styling across the app
  
  // Card decorations
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: _backgroundLightColor,
    borderRadius: mediumRadius,
    boxShadow: subtleShadow,
    border: Border.all(
      color: _textTertiaryLightColor.withOpacity(0.1),
      width: 1,
    ),
  );
  
  static BoxDecoration get elevatedCardDecoration => BoxDecoration(
    color: _backgroundLightColor,
    borderRadius: mediumRadius,
    boxShadow: mediumShadow,
  );
  
  // Gradient decorations
  static BoxDecoration get primaryGradientDecoration => BoxDecoration(
    gradient: primaryGradient,
    borderRadius: mediumRadius,
    boxShadow: mediumShadow,
  );
  
  static BoxDecoration get accentGradientDecoration => BoxDecoration(
    gradient: accentGradient,
    borderRadius: mediumRadius,
    boxShadow: mediumShadow,
  );
  
  // Status indicator decorations
  static BoxDecoration statusIndicator(Color color) => BoxDecoration(
    color: color.withOpacity(0.1),
    borderRadius: circularRadius,
    border: Border.all(
      color: color.withOpacity(0.6),
      width: 1.5,
    ),
  );
  
  // Luxurious container decoration
  static BoxDecoration get luxuryContainer => BoxDecoration(
    color: _backgroundLightColor,
    borderRadius: mediumRadius,
    boxShadow: subtleShadow,
    border: Border.all(
      color: _accentColor.withOpacity(0.2),
      width: 1.5,
    ),
  );
  
  // Frosted glass effect
  static BoxDecoration get frostedGlass => BoxDecoration(
    color: Colors.white.withOpacity(0.15),
    borderRadius: mediumRadius,
    border: Border.all(
      color: Colors.white.withOpacity(0.2),
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.05),
        blurRadius: 20,
        spreadRadius: -5,
      ),
    ],
  );
} 