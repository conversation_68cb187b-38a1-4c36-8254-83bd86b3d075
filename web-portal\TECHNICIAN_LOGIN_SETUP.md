# Technician Login Setup Guide

## 🔐 How Technicians Can Login to the Web Portal

### Current System Overview

The web portal uses a role-based authentication system where:
- **Admins** can manage all aspects of the system
- **Technicians** can access their profiles, schedules, and assigned requests  
- **Customers** are blocked from accessing the web portal (mobile app only)

### Setting Up Technician Login Accounts

When you create a technician profile in the web portal, you have two options:

#### Option 1: Manual Setup (Current)
1. **Create Technician Profile** - Use the "Add Technician" button to create their profile
2. **Create Firebase User Account** - Manually add to Firebase Authentication:
   - Go to Firebase Console → Authentication → Users
   - Click "Add User"
   - Enter the technician's email and a temporary password
   - The technician can change this later

3. **Create User Document** - Add a document in the `users` collection:
```json
{
  "email": "<EMAIL>",
  "name": "<PERSON>e",
  "role": "technician", 
  "phone": "+**********",
  "is_active": true,
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

#### Option 2: Automated Setup (Future Enhancement)
The form now includes options for:
- ✅ **Create User Account** checkbox
- 🔑 **Password** field (when checkbox is enabled)

*Note: This requires backend integration with Firebase Admin SDK to create accounts programmatically.*

### Technician Login Process

1. **Access the Portal** - Technicians go to the same login page as admins
2. **Enter Credentials** - Use their email and password
3. **Automatic Role Detection** - System automatically redirects based on role:
   - Admins see full management interface
   - Technicians see their personalized dashboard
4. **Permission-Based Access** - Each feature checks user permissions

### Required Firebase Collections

**users** (for login accounts):
```json
{
  "uid": "firebase-user-id",
  "email": "<EMAIL>", 
  "name": "John Doe",
  "role": "technician",
  "phone": "+**********",
  "is_active": true,
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

**technicians** (for profile data):
```json
{
  "id": "firebase-user-id", // Same as user uid
  "email": "<EMAIL>",
  "name": "John Doe", 
  "specialties": ["Computer Repair", "Mobile Repair"],
  "status": "active",
  "is_available": true,
  "rating": 4.5,
  "completed_requests": 15,
  "active_requests": 2
}
```

### Security Features

- ✅ **Role-based access control** - Customers cannot access web portal
- ✅ **Permission gates** - Each feature checks appropriate permissions  
- ✅ **Session management** - Secure login/logout with remember me option
- ✅ **Account status checking** - Inactive accounts are blocked
- ✅ **Password reset** - Forgot password functionality available

### Next Steps

To fully automate technician account creation:

1. **Set up Firebase Admin SDK** on the backend
2. **Create Cloud Function** to handle account creation
3. **Update the form handler** to call the function
4. **Add email notifications** to send login credentials

### Troubleshooting

**Technician can't login?**
- ✅ Check if user exists in Firebase Authentication
- ✅ Verify user document exists in `users` collection with `role: "technician"`
- ✅ Confirm `is_active: true` in user document
- ✅ Check if email/password are correct

**Technician sees "access denied"?**
- ✅ Verify `role` field is set to "technician" (not "customer")
- ✅ Check if user document exists in Firestore
- ✅ Confirm account is active (`is_active: true`)

**Profile not showing up?**
- ✅ Ensure technician document exists with required fields
- ✅ Check browser console for errors
- ✅ Verify document ID matches user UID 