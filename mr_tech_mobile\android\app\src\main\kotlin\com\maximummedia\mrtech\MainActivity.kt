package com.maximummedia.mrtech

import android.os.Bundle
import android.content.pm.PackageManager
import android.util.Log
import io.flutter.embedding.android.FlutterActivity

class MainActivity: FlutterActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Print SHA-1 for debugging Google Sign-In
        try {
            val info = packageManager.getPackageInfo(packageName, PackageManager.GET_SIGNATURES)
            info.signatures?.forEach { signature ->
                val md = java.security.MessageDigest.getInstance("SHA-1")
                md.update(signature.toByteArray())
                val sha1 = java.math.BigInteger(1, md.digest()).toString(16).padStart(40, '0')
                Log.d("KeyHash", "SHA1: $sha1")
            }
        } catch (e: Exception) {
            Log.e("KeyHash", "Error getting SHA1: ${e.message}")
        }
    }
} 