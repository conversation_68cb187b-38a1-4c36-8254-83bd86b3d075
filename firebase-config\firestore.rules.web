rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Check if the user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Check if the user has the admin role
    function isAdmin() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Check if the user has the technician role
    function isTechnician() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'technician';
    }
    
    // Check if the request is from the user's own document
    function isUser(userId) {
      return request.auth.uid == userId;
    }
    
    // Match all documents in the users collection
    match /users/{userId} {
      // Allow any authenticated user to read their own document
      // Allow admins to read all user documents
      allow read: if isUser(userId) || isAdmin();
      
      // Allow users to create their own document
      allow create: if isUser(userId);
      
      // Allow users to update their own document (excluding the role field)
      // Allow admins to update any user document (including the role field)
      allow update: if (isUser(userId) && !request.resource.data.diff(resource.data).affectedKeys().hasAny(['role'])) || 
                     isAdmin();
                     
      // Only admins can delete user documents
      allow delete: if isAdmin();
    }
    
    // Match all documents in the requests collection
    match /requests/{requestId} {
      // Admins can read all requests
      // Technicians can read requests assigned to them or unassigned requests
      // Clients can read their own requests
      allow read: if isAdmin() || 
                   (isTechnician() && (resource.data.assignedTo == request.auth.uid || resource.data.assignedTo == null)) ||
                   (isAuthenticated() && resource.data.clientId == request.auth.uid);
      
      // Clients can create new requests
      // System can create requests (via Cloud Functions)
      allow create: if isAuthenticated() && request.resource.data.clientId == request.auth.uid;
      
      // Admins can update any request
      // Technicians can update requests assigned to them
      allow update: if isAdmin() ||
                     (isTechnician() && resource.data.assignedTo == request.auth.uid);
      
      // Only admins can delete requests               
      allow delete: if isAdmin();
    }
    
    // Match all documents in the services collection
    match /services/{serviceId} {
      // Anyone can read services
      allow read: if true;
      
      // Only admins can create, update, or delete services
      allow write: if isAdmin();
    }
    
    // Match all documents in the reviews collection
    match /reviews/{reviewId} {
      // Anyone can read reviews
      allow read: if true;
      
      // Authenticated users can create reviews for requests they own
      allow create: if isAuthenticated() && 
                     exists(/databases/$(database)/documents/requests/$(request.resource.data.requestId)) &&
                     get(/databases/$(database)/documents/requests/$(request.resource.data.requestId)).data.clientId == request.auth.uid;
      
      // Only admins can update or delete reviews
      allow update, delete: if isAdmin();
    }
  }
} 