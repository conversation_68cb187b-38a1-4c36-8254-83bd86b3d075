import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/auth_service.dart';
import '../views/main_navigation.dart';
import '../widgets/text_styles.dart';
import 'package:firebase_auth/firebase_auth.dart';

class LoginScreen extends StatefulWidget {
  final VoidCallback? onLoginSuccess;

  const LoginScreen({super.key, this.onLoginSuccess});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _hidePassword = true;
  String? _errorMessage;

  // Keep track of failed login attempts locally for feedback
  int _localFailedAttempts = 0;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // Sanitize email input
  String _sanitizeEmail(String email) {
    return email.trim().toLowerCase();
  }

  Future<void> _signInWithEmailAndPassword() async {
    // Clear previous errors
    setState(() {
      _errorMessage = null;
    });

    // Validate the form
    if (!_formKey.currentState!.validate()) return;

    // Sanitize input
    final sanitizedEmail = _sanitizeEmail(_emailController.text);

    setState(() => _isLoading = true);

    try {
      // Use AuthService to sign in with email and password
      final authService = AuthService();
      await authService.signInWithEmailAndPassword(
        sanitizedEmail,
        _passwordController.text,
      );

      // Reset local failed attempts counter on success
      _localFailedAttempts = 0;

      // Call the success callback immediately to trigger stream listener in welcome screen
      if (widget.onLoginSuccess != null) {
        widget.onLoginSuccess!();
      }

      // Check if the widget is still mounted before attempting to navigate
      if (mounted) {
        // Navigate directly to MainNavigation screen
        // This will bypass the welcome screen and ensure user goes to home immediately
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const MainNavigation()),
          (route) => false, // This removes all previous routes
        );
      }
    } catch (e) {
      // Increment local failed attempts counter
      _localFailedAttempts++;

      // Use sanitized error messages
      setState(() {
        if (e is Exception) {
          _errorMessage = e.toString().replaceFirst('Exception: ', '');
        } else if (e is FirebaseAuthException) {
          // Use the error code to determine the message
          switch (e.code) {
            case 'user-not-found':
              _errorMessage = 'No user found with this email.';
              break;
            case 'wrong-password':
              _errorMessage = 'Incorrect password. Please try again.';
              break;
            case 'user-disabled':
              _errorMessage = 'This account has been disabled.';
              break;
            case 'too-many-requests':
              _errorMessage =
                  'Too many login attempts. Please try again later.';
              break;
            case 'network-request-failed':
              _errorMessage = 'Network error. Check your connection.';
              break;
            default:
              _errorMessage = 'Authentication failed. Please try again.';
          }
        } else {
          _errorMessage = 'Authentication failed. Please try again.';
        }

        // Add a warning if there are multiple failed attempts
        if (_localFailedAttempts >= 3) {
          _errorMessage =
              '$_errorMessage\nMultiple failed attempts may lead to a temporary account lock.';
        }
      });
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _signInWithGoogle() async {
    // Clear previous errors
    setState(() {
      _errorMessage = null;
    });

    setState(() => _isLoading = true);

    try {
      // Use AuthService to sign in with Google
      final authService = AuthService();
      await authService.signInWithGoogle();

      // Reset local failed attempts counter on success
      _localFailedAttempts = 0;

      // Call the success callback immediately to trigger stream listener in welcome screen
      if (widget.onLoginSuccess != null) {
        widget.onLoginSuccess!();
      }

      // Check if the widget is still mounted before attempting to navigate
      if (mounted) {
        // Navigate directly to MainNavigation screen
        // This will bypass the welcome screen and ensure user goes to home immediately
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const MainNavigation()),
          (route) => false, // This removes all previous routes
        );
      }
    } catch (e) {
      // Log detailed error for debugging
      debugPrint('Google Sign-In Error in UI: $e');
      if (e.toString().contains('PlatformException')) {
        debugPrint('Platform Exception details: $e');
      }

      // Use sanitized error messages
      setState(() {
        if (e is Exception) {
          _errorMessage = e.toString().replaceFirst('Exception: ', '');
        } else {
          _errorMessage = 'Google sign-in failed. Please try again.';
        }
      });
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(context);
    final translate = translationService.translate;
    final isArabic = translationService.currentLocale.languageCode == 'ar';
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: BackButton(color: colorScheme.onSurface),
        title: Text(
          translate('Sign In'),
          style: AppTextStyles.headingMedium(
            context,
            color: colorScheme.onSurface,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Simplified logo without shadow
                  Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.grey.shade300,
                          width: 2,
                        ),
                        color: colorScheme.surface,
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          'assets/images/logo.png',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.engineering,
                              size: 40,
                              color: colorScheme.primary,
                            );
                          },
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Welcome back text
                  Text(
                    translate('Welcome Back'),
                    style: AppTextStyles.headingLarge(
                      context,
                      color: colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 8),

                  Text(
                    translate('Enter your credentials to continue'),
                    style: AppTextStyles.bodyMedium(
                      context,
                      color: colorScheme.onSurface.withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 32),

                  // Error message if any
                  if (_errorMessage != null) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: colorScheme.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colorScheme.error.withOpacity(0.3),
                        ),
                      ),
                      child: Text(
                        _errorMessage!,
                        style: AppTextStyles.bodyMedium(
                          context,
                          color: colorScheme.error,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Email field
                  _buildTextField(
                    controller: _emailController,
                    label: translate('Email'),
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    prefixIcon: Icons.email_outlined,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return translate('Please enter your email');
                      }
                      // Enhanced email validation
                      final emailPattern = RegExp(
                        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                      );
                      if (!emailPattern.hasMatch(value.trim())) {
                        return translate('Please enter a valid email address');
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Password field
                  _buildTextField(
                    controller: _passwordController,
                    label: translate('Password'),
                    obscureText: _hidePassword,
                    textInputAction: TextInputAction.done,
                    prefixIcon: Icons.lock_outline,
                    suffixIcon:
                        _hidePassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                    onSuffixIconTap: () {
                      setState(() {
                        _hidePassword = !_hidePassword;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return translate('Please enter your password');
                      }
                      // Basic length validation (full validation is done by AuthService)
                      if (value.length < 6) {
                        return translate(
                          'Password must be at least 6 characters',
                        );
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 8),

                  // Forgot password link
                  Align(
                    alignment:
                        isArabic ? Alignment.centerLeft : Alignment.centerRight,
                    child: TextButton(
                      onPressed: () {
                        // Show password reset dialog
                        _showResetPasswordDialog(context, translate);
                      },
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.zero,
                        minimumSize: const Size(0, 36),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        translate('Forgot Password?'),
                        style: AppTextStyles.bodyMedium(
                          context,
                          color: colorScheme.primary,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Sign in button
                  SizedBox(
                    height: 52,
                    child: FilledButton(
                      onPressed:
                          _isLoading ? null : _signInWithEmailAndPassword,
                      style: FilledButton.styleFrom(
                        backgroundColor: colorScheme.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child:
                          _isLoading
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : Text(
                                translate('Sign In'),
                                style: AppTextStyles.buttonMedium(
                                  context,
                                  color: Colors.white,
                                ),
                              ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // OR divider
                  Row(
                    children: [
                      Expanded(
                        child: Divider(
                          color: Colors.grey.shade300,
                          thickness: 1,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          translate('OR'),
                          style: AppTextStyles.bodyMedium(
                            context,
                            color: colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Divider(
                          color: Colors.grey.shade300,
                          thickness: 1,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Google sign in button
                  SizedBox(
                    height: 52,
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : _signInWithGoogle,
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey.shade300),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        backgroundColor: Colors.white,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/google_logo.png',
                            height: 24,
                            width: 24,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.login,
                                size: 24,
                                color: colorScheme.primary,
                              );
                            },
                          ),
                          const SizedBox(width: 12),
                          Text(
                            translate('Sign in with Google'),
                            style: AppTextStyles.buttonMedium(
                              context,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build text fields with consistent styling
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixIconTap,
    bool obscureText = false,
    String? Function(String?)? validator,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      obscureText: obscureText,
      validator: validator,
      style: AppTextStyles.bodyMedium(context),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: AppTextStyles.bodyMedium(
          context,
          color: colorScheme.onSurface.withOpacity(0.7),
        ),
        prefixIcon:
            prefixIcon != null
                ? Icon(
                  prefixIcon,
                  color: colorScheme.onSurface.withOpacity(0.7),
                )
                : null,
        suffixIcon:
            suffixIcon != null
                ? IconButton(
                  icon: Icon(
                    suffixIcon,
                    color: colorScheme.onSurface.withOpacity(0.7),
                  ),
                  onPressed: onSuffixIconTap,
                )
                : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.error, width: 2),
        ),
        filled: true,
        fillColor: colorScheme.surface,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  // Show password reset dialog
  void _showResetPasswordDialog(
    BuildContext context,
    String Function(String) translate,
  ) {
    final emailController = TextEditingController();
    final formKey = GlobalKey<FormState>();
    String? errorMessage;
    bool isLoading = false;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              title: Text(
                translate('Reset Password'),
                style: AppTextStyles.headingMedium(context),
              ),
              content: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      translate(
                        'Enter your email to receive a password reset link',
                      ),
                      style: AppTextStyles.bodyMedium(context),
                    ),
                    const SizedBox(height: 16),
                    if (errorMessage != null) ...[
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Theme.of(
                            context,
                          ).colorScheme.error.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Theme.of(
                              context,
                            ).colorScheme.error.withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          errorMessage!,
                          style: AppTextStyles.bodySmall(
                            context,
                            color: Theme.of(context).colorScheme.error,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    _buildTextField(
                      controller: emailController,
                      label: translate('Email'),
                      keyboardType: TextInputType.emailAddress,
                      prefixIcon: Icons.email_outlined,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return translate('Please enter your email');
                        }
                        // Enhanced email validation
                        final emailPattern = RegExp(
                          r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                        );
                        if (!emailPattern.hasMatch(value.trim())) {
                          return translate(
                            'Please enter a valid email address',
                          );
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    translate('Cancel'),
                    style: AppTextStyles.buttonMedium(context),
                  ),
                ),
                isLoading
                    ? const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                    : FilledButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          setState(() {
                            isLoading = true;
                            errorMessage = null;
                          });

                          try {
                            final authService = AuthService();
                            await authService.sendPasswordResetEmail(
                              _sanitizeEmail(emailController.text),
                            );

                            if (mounted) {
                              Navigator.of(context).pop();
                              // Show success message
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    translate(
                                      'Password reset link sent to your email',
                                    ),
                                  ),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            }
                          } catch (e) {
                            setState(() {
                              isLoading = false;
                              if (e is Exception) {
                                errorMessage = e.toString().replaceFirst(
                                  'Exception: ',
                                  '',
                                );
                              } else {
                                errorMessage = translate(
                                  'Failed to send reset link. Please try again.',
                                );
                              }
                            });
                          }
                        }
                      },
                      child: Text(
                        translate('Send Link'),
                        style: AppTextStyles.buttonMedium(
                          context,
                          color: Colors.white,
                        ),
                      ),
                    ),
              ],
            );
          },
        );
      },
    );
  }
}
