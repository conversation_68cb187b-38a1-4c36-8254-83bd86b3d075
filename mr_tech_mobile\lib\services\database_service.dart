import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart' hide Query;
import 'dart:async';
import '../models/user_model.dart';
import '../models/request_model.dart';
import '../models/service_model.dart';
import '../models/chat_message_model.dart';
import 'package:flutter/foundation.dart';

class DatabaseService {
  late final FirebaseFirestore _firestore;
  late final FirebaseAuth _auth;
  late final FirebaseDatabase _realtimeDb;

  // Collection references
  late final CollectionReference _usersCollection;
  late final CollectionReference _serviceRequestsCollection;
  late final CollectionReference _servicesCollection;
  late final CollectionReference _techniciansCollection;
  late final CollectionReference _reviewsCollection;

  // Cache implementation
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Duration _defaultCacheExpiry = Duration(minutes: 15);

  // Constructor
  DatabaseService() {
    _firestore = FirebaseFirestore.instance;
    _auth = FirebaseAuth.instance;
    _realtimeDb = FirebaseDatabase.instance;

    // Initialize collection references
    _usersCollection = _firestore.collection('users');
    _serviceRequestsCollection = _firestore.collection('service_requests');
    _servicesCollection = _firestore.collection('services');
    _techniciansCollection = _firestore.collection('technicians');
    _reviewsCollection = _firestore.collection('reviews');
  }

  // Get current user ID helper
  String? get currentUserId => _auth.currentUser?.uid;

  // Cache helper methods
  bool _isCacheValid(String key, [Duration? customExpiry]) {
    final expiry = customExpiry ?? _defaultCacheExpiry;
    if (_cache.containsKey(key) && _cacheTimestamps.containsKey(key)) {
      final timestamp = _cacheTimestamps[key]!;
      return DateTime.now().difference(timestamp) < expiry;
    }
    return false;
  }

  void _updateCache(String key, dynamic data) {
    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
  }

  void invalidateCache(String key) {
    _cache.remove(key);
    _cacheTimestamps.remove(key);
  }

  void clearAllCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  // Get services with caching
  Future<List<ServiceModel>> getServices() async {
    const cacheKey = 'all_services';

    // Check cache first
    if (_isCacheValid(cacheKey)) {
      debugPrint('Using cached services data');
      return _cache[cacheKey] as List<ServiceModel>;
    }

    // Cache miss or expired, fetch from Firestore
    debugPrint('Fetching services from Firestore');
    try {
      final snapshot = await _servicesCollection.get();
      final services =
          snapshot.docs
              .map(
                (doc) => ServiceModel.fromFirestore(
                  doc as DocumentSnapshot<Map<String, dynamic>>,
                ),
              )
              .toList();

      // Update cache
      _updateCache(cacheKey, services);

      return services;
    } catch (e) {
      debugPrint('Error fetching services: $e');
      // If we have stale cache, return that rather than nothing
      if (_cache.containsKey(cacheKey)) {
        debugPrint('Returning stale services cache due to error');
        return _cache[cacheKey] as List<ServiceModel>;
      }
      rethrow;
    }
  }

  // Get user with caching
  Future<UserModel?> getUserById(
    String userId, {
    bool forceRefresh = false,
  }) async {
    final cacheKey = 'user_$userId';

    // Check cache first unless force refresh is requested
    if (!forceRefresh && _isCacheValid(cacheKey)) {
      debugPrint('Using cached user data for $userId');
      return _cache[cacheKey] as UserModel?;
    }

    // Cache miss, expired, or force refresh - fetch from Firestore
    debugPrint('Fetching user $userId from Firestore');
    try {
      final docSnapshot = await _usersCollection.doc(userId).get();

      if (!docSnapshot.exists) {
        // If user doesn't exist, cache null with shorter expiry
        _updateCache(cacheKey, null);
        return null;
      }

      final user = UserModel.fromFirestore(
        docSnapshot as DocumentSnapshot<Map<String, dynamic>>,
      );

      // Update cache
      _updateCache(cacheKey, user);

      return user;
    } catch (e) {
      debugPrint('Error fetching user: $e');
      // If we have stale cache and it's not a force refresh, return that
      if (!forceRefresh && _cache.containsKey(cacheKey)) {
        debugPrint('Returning stale user cache due to error');
        return _cache[cacheKey] as UserModel?;
      }
      rethrow;
    }
  }

  // Get service request with caching (shorter cache time for active requests)
  Future<RequestModel?> getRequestById(
    String requestId, {
    bool forceRefresh = false,
  }) async {
    final cacheKey = 'request_$requestId';

    // For active requests, use shorter cache time or force refresh
    final customExpiry = Duration(
      minutes: 5,
    ); // Shorter cache time for requests

    // Check cache first unless force refresh is requested
    if (!forceRefresh && _isCacheValid(cacheKey, customExpiry)) {
      debugPrint('Using cached request data for $requestId');
      return _cache[cacheKey] as RequestModel?;
    }

    // Cache miss, expired, or force refresh - fetch from Firestore
    debugPrint('Fetching request $requestId from Firestore');
    try {
      final docSnapshot = await _serviceRequestsCollection.doc(requestId).get();

      if (!docSnapshot.exists) {
        return null;
      }

      final request = RequestModel.fromFirestore(
        docSnapshot as DocumentSnapshot<Map<String, dynamic>>,
      );

      // Update cache - active requests have shorter cache time
      _updateCache(cacheKey, request);

      return request;
    } catch (e) {
      debugPrint('Error fetching request: $e');
      // If we have stale cache and it's not a force refresh, return that
      if (!forceRefresh && _cache.containsKey(cacheKey)) {
        debugPrint('Returning stale request cache due to error');
        return _cache[cacheKey] as RequestModel?;
      }
      rethrow;
    }
  }

  // Get paginated requests for better performance and reduced reads
  // PHASE 1: Updated to use snake_case field names for queries
  Future<List<RequestModel>> getUserRequestsPaginated({
    required String userId,
    int limit = 10,
    DocumentSnapshot? lastDocument,
    RequestStatus? statusFilter,
  }) async {
    try {
      // Phase 1: Use snake_case field names in queries (primary)
      // Note: Firestore indexes should support both formats during migration
      Query query = _serviceRequestsCollection
          .where('customer_id', isEqualTo: userId) // Updated to snake_case
          .orderBy('created_at', descending: true); // Already snake_case

      // Add status filter if specified
      if (statusFilter != null) {
        query = query.where(
          'status',
          isEqualTo: statusFilter.toString().split('.').last,
        );
      }

      // Apply pagination
      query = query.limit(limit);

      // Add starting point if we have a last document
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();

      // Use the new fromMap method for consistent parsing
      return querySnapshot.docs
          .map(
            (doc) => RequestModel.fromMap(
              doc.id,
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('Error getting paginated requests: $e');
      rethrow;
    }
  }

  // Initialize Firestore with demo data if needed
  Future<void> initializeFirestoreCollections() async {
    await _initializeServices();
    await _initializeDefaultTechnicians();
  }

  // Initialize services collection with default services
  Future<void> _initializeServices() async {
    try {
      // Check if services collection is empty
      final servicesSnapshot = await _servicesCollection.limit(1).get();

      if (servicesSnapshot.docs.isEmpty) {
        debugPrint('Initializing services collection with default data');

        // Add sample services with translations
        final services = [
          {
            'name': {'en': 'Windows Support', 'ar': 'دعم ويندوز'},
            'description': {
              'en':
                  'Get expert support for Windows OS issues, performance optimization, troubleshooting, and system recovery.',
              'ar':
                  'احصل على دعم متخصص لمشاكل نظام ويندوز، وتحسين الأداء، واستكشاف الأخطاء وإصلاحها، واستعادة النظام.',
            },
            'category': 'os',
            'basePrice': 39.99,
            'estimatedDuration': 60,
            'isActive': true,
            'metadata': {'icon': 'computer', 'popularityIndex': 8},
          },
          {
            'name': {'en': 'Office Applications', 'ar': 'تطبيقات أوفيس'},
            'description': {
              'en':
                  'Get help with Microsoft Office applications including Word, Excel, PowerPoint, and Outlook.',
              'ar':
                  'احصل على مساعدة في تطبيقات مايكروسوفت أوفيس بما في ذلك وورد، إكسل، باوربوينت، وأوتلوك.',
            },
            'category': 'productivity',
            'basePrice': 29.99,
            'estimatedDuration': 45,
            'isActive': true,
            'metadata': {'icon': 'article', 'popularityIndex': 7},
          },
          {
            'name': {'en': 'Printer Setup', 'ar': 'إعداد الطابعة'},
            'description': {
              'en':
                  'Professional assistance setting up and troubleshooting printers, scanners, and all-in-one devices.',
              'ar':
                  'مساعدة احترافية في إعداد وإصلاح مشاكل الطابعات والماسحات الضوئية والأجهزة متعددة الوظائف.',
            },
            'category': 'hardware',
            'basePrice': 24.99,
            'estimatedDuration': 30,
            'isActive': true,
            'metadata': {'icon': 'print', 'popularityIndex': 6},
          },
          {
            'name': {
              'en': 'Network Troubleshooting',
              'ar': 'استكشاف أخطاء الشبكة وإصلاحها',
            },
            'description': {
              'en':
                  'Fix Wi-Fi issues, network connectivity problems, and optimize your home or office network.',
              'ar':
                  'إصلاح مشاكل الواي فاي، ومشاكل الاتصال بالشبكة، وتحسين شبكة المنزل أو المكتب.',
            },
            'category': 'network',
            'basePrice': 49.99,
            'estimatedDuration': 60,
            'isActive': true,
            'metadata': {'icon': 'wifi', 'popularityIndex': 9},
          },
          {
            'name': {'en': 'Virus Removal', 'ar': 'إزالة الفيروسات'},
            'description': {
              'en':
                  'Remove viruses, malware, and other security threats from your computer and improve system security.',
              'ar':
                  'إزالة الفيروسات والبرامج الضارة والتهديدات الأمنية الأخرى من جهاز الكمبيوتر الخاص بك وتحسين أمان النظام.',
            },
            'category': 'security',
            'basePrice': 59.99,
            'estimatedDuration': 90,
            'isActive': true,
            'metadata': {'icon': 'security', 'popularityIndex': 10},
          },
          {
            'name': {'en': 'Data Recovery', 'ar': 'استعادة البيانات'},
            'description': {
              'en':
                  'Recover lost or deleted files, photos, and important documents from your computer or external drives.',
              'ar':
                  'استعادة الملفات المفقودة أو المحذوفة والصور والمستندات المهمة من جهاز الكمبيوتر أو محركات الأقراص الخارجية.',
            },
            'category': 'data',
            'basePrice': 69.99,
            'estimatedDuration': 120,
            'isActive': true,
            'metadata': {'icon': 'backup', 'popularityIndex': 8},
          },
        ];

        // Add each service to Firestore
        for (final service in services) {
          await _servicesCollection.add(service);
        }

        debugPrint('Services collection initialized successfully');
      }
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  // Initialize technicians collection with default technicians
  Future<void> _initializeDefaultTechnicians() async {
    try {
      // Check if technicians collection is empty
      final techniciansSnapshot = await _techniciansCollection.limit(1).get();

      if (techniciansSnapshot.docs.isEmpty) {
        debugPrint('Initializing technicians collection with default data');

        // Add sample technicians
        final technicians = [
          {
            'name': 'Ahmed Khalid',
            'email': '<EMAIL>',
            'specialties': ['Windows', 'Office', 'Networks'],
            'rating': 4.8,
            'totalReviews': 127,
            'isAvailable': true,
            'photoUrl': 'https://randomuser.me/api/portraits/men/32.jpg',
          },
          {
            'name': 'Sarah Mohamed',
            'email': '<EMAIL>',
            'specialties': ['Office', 'Data Recovery', 'Printer Setup'],
            'rating': 4.7,
            'totalReviews': 98,
            'isAvailable': true,
            'photoUrl': 'https://randomuser.me/api/portraits/women/44.jpg',
          },
          {
            'name': 'Omar Hassan',
            'email': '<EMAIL>',
            'specialties': ['Security', 'Virus Removal', 'Windows'],
            'rating': 4.9,
            'totalReviews': 156,
            'isAvailable': true,
            'photoUrl': 'https://randomuser.me/api/portraits/men/62.jpg',
          },
        ];

        // Add each technician to Firestore
        for (final technician in technicians) {
          await _techniciansCollection.add(technician);
        }

        debugPrint('Technicians collection initialized successfully');
      }
    } catch (e) {
      debugPrint('Error initializing technicians: $e');
    }
  }

  // Create or update user profile
  Future<void> createOrUpdateUser(UserModel user) async {
    try {
      final docRef = _usersCollection.doc(user.id);

      // Check if the user already exists
      final docSnapshot = await docRef.get();
      if (docSnapshot.exists) {
        // Update only the fields that are provided
        await docRef.update(user.toMap());
      } else {
        // Create new user
        await docRef.set(user.toMap());
      }
    } catch (e) {
      debugPrint('Error creating/updating user: $e');
      rethrow;
    }
  }

  // Get current user
  Future<UserModel?> getCurrentUser() async {
    if (_auth.currentUser == null) return null;

    try {
      UserModel? userModel = await getUserById(_auth.currentUser!.uid);

      // If the user model exists but doesn't have a display name, use Firebase Auth data
      if (userModel != null &&
          (userModel.displayName == null || userModel.displayName!.isEmpty)) {
        final User firebaseUser = _auth.currentUser!;
        // Update the user model with display name and photo URL from Firebase Auth
        if (firebaseUser.displayName != null &&
            firebaseUser.displayName!.isNotEmpty) {
          userModel = userModel.copyWith(
            displayName: firebaseUser.displayName,
            photoUrl: firebaseUser.photoURL ?? userModel.photoUrl,
          );

          // Update the Firestore document with the Firebase Auth data
          await _usersCollection.doc(firebaseUser.uid).update({
            'display_name': firebaseUser.displayName,
            'photo_url': firebaseUser.photoURL,
            'updated_at': FieldValue.serverTimestamp(),
          });
        }
      }

      return userModel;
    } catch (e) {
      debugPrint('Error getting current user: $e');
      rethrow;
    }
  }

  // Create a new request
  Future<String> createRequest(RequestModel request) async {
    try {
      final docRef = await _serviceRequestsCollection.add(
        request.toFirestore(),
      );
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating request: $e');
      rethrow;
    }
  }

  // Get all requests for current user
  Future<List<RequestModel>> getUserRequests() async {
    if (_auth.currentUser == null) return [];

    try {
      final querySnapshot =
          await _serviceRequestsCollection
              .where('customer_id', isEqualTo: _auth.currentUser!.uid)
              .orderBy('created_at', descending: true)
              .get();

      return querySnapshot.docs
          .map(
            (doc) => RequestModel.fromMap(
              doc.id,
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('Error getting user requests: $e');
      rethrow;
    }
  }

  // Get all active (in progress) requests for current user
  Future<List<RequestModel>> getUserActiveRequests() async {
    if (_auth.currentUser == null) return [];

    try {
      final querySnapshot =
          await _serviceRequestsCollection
              .where('customer_id', isEqualTo: _auth.currentUser!.uid)
              .where(
                'status',
                whereIn: [
                  RequestStatus.pending.toString().split('.').last,
                  RequestStatus.approved.toString().split('.').last,
                  RequestStatus.inProgress.toString().split('.').last,
                ],
              )
              .orderBy('created_at', descending: true)
              .get();

      return querySnapshot.docs
          .map(
            (doc) => RequestModel.fromMap(
              doc.id,
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('Error getting active requests: $e');
      rethrow;
    }
  }

  // Stream active requests for the current user to get real-time updates
  Stream<List<RequestModel>> streamUserActiveRequests() {
    if (_auth.currentUser == null) return Stream.value([]);

    try {
      debugPrint('Setting up stream for active requests');
      return _serviceRequestsCollection
          .where('customer_id', isEqualTo: _auth.currentUser!.uid)
          .where(
            'status',
            whereIn: [
              RequestStatus.pending.toString().split('.').last,
              RequestStatus.approved.toString().split('.').last,
              RequestStatus.inProgress.toString().split('.').last,
              'pending',
              'approved',
              'in-progress',
            ],
          )
          .orderBy('created_at', descending: true)
          .snapshots()
          .map((querySnapshot) {
            debugPrint(
              'Received updated active requests: ${querySnapshot.docs.length}',
            );
            return querySnapshot.docs
                .map(
                  (doc) => RequestModel.fromMap(
                    doc.id,
                    doc.data() as Map<String, dynamic>,
                  ),
                )
                .toList();
          });
    } catch (e) {
      debugPrint('Error streaming active requests: $e');
      // Return an empty stream that emits an error
      return Stream.value([]);
    }
  }

  // Check if user has any active requests
  // Returns true if the user has at least one active request
  Future<bool> hasActiveRequests() async {
    if (_auth.currentUser == null) return false;

    try {
      final querySnapshot =
          await _serviceRequestsCollection
              .where('customer_id', isEqualTo: _auth.currentUser!.uid)
              .where(
                'status',
                whereIn: [
                  RequestStatus.pending.toString().split('.').last,
                  RequestStatus.approved.toString().split('.').last,
                  RequestStatus.inProgress.toString().split('.').last,
                ],
              )
              .limit(1) // Only need to know if at least one exists
              .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking active requests: $e');
      rethrow;
    }
  }

  // Create a new request, but first check if the user already has active requests
  Future<String> createRequestIfNoActive(RequestModel request) async {
    try {
      // Check if user already has active requests
      final hasActive = await hasActiveRequests();

      if (hasActive) {
        throw Exception(
          'You already have an active request. Please wait until your current request is completed before creating a new one.',
        );
      }

      // If no active requests, create a new one
      final docRef = await _serviceRequestsCollection.add(
        request.toFirestore(),
      );
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating request: $e');
      rethrow;
    }
  }

  // Get all active services
  Future<List<ServiceModel>> getActiveServices() async {
    try {
      // Phase 2: Query using snake_case field name
      final querySnapshot =
          await _servicesCollection.where('is_active', isEqualTo: true).get();

      return querySnapshot.docs
          .map(
            (doc) => ServiceModel.fromMap(
              doc.id,
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('Error getting active services: $e');
      rethrow;
    }
  }

  // Get service by ID
  Future<ServiceModel?> getServiceById(String serviceId) async {
    try {
      final docSnapshot = await _servicesCollection.doc(serviceId).get();
      if (docSnapshot.exists) {
        return ServiceModel.fromMap(
          docSnapshot.id,
          docSnapshot.data() as Map<String, dynamic>,
        );
      }
      return null;
    } catch (e) {
      debugPrint('Error getting service by ID: $e');
      rethrow;
    }
  }

  // Update request status
  Future<void> updateRequestStatus(
    String requestId,
    RequestStatus status,
  ) async {
    try {
      await _serviceRequestsCollection.doc(requestId).update({
        'status': status.toString().split('.').last,
      });
    } catch (e) {
      debugPrint('Error updating request status: $e');
      rethrow;
    }
  }

  // Mark request as paid
  Future<void> markRequestAsPaid(String requestId) async {
    try {
      await _serviceRequestsCollection.doc(requestId).update({'is_paid': true});
    } catch (e) {
      debugPrint('Error marking request as paid: $e');
      rethrow;
    }
  }

  // Chat methods using Firebase Realtime Database

  /// Send a chat message to both Firestore and Realtime Database
  Future<void> sendChatMessage(
    ChatMessageModel message,
    String requestId,
  ) async {
    try {
      debugPrint('Sending chat message for request: $requestId');

      // Generate ID first
      final DocumentReference messageRef =
          _firestore
              .collection('service_requests')
              .doc(requestId)
              .collection('messages')
              .doc(); // Auto-generate ID

      // Create a copy of the message with the generated ID
      final updatedMessage = message.copyWith(id: messageRef.id);

      // Prepare data for both databases
      final firestoreData = updatedMessage.toFirestore();
      final realtimeData = updatedMessage.toJson();

      // Add to Firestore
      await messageRef.set(firestoreData);

      // Add to Realtime Database
      await _realtimeDb
          .ref()
          .child('messages')
          .child(requestId)
          .child(messageRef.id)
          .set(realtimeData);

      // Update last_message in the request document
      await _firestore.collection('service_requests').doc(requestId).update({
        'last_message': firestoreData['content'],
        'last_message_time': FieldValue.serverTimestamp(),
        'last_message_sender': firestoreData['sender_type'],
        // Update for both naming conventions to ensure compatibility
        'updated_at': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('Chat message sent successfully');
    } catch (e) {
      debugPrint('Error sending chat message: $e');
      rethrow;
    }
  }

  /// Stream chat messages for a specific request
  Stream<List<ChatMessageModel>> streamChatMessages(String requestId) {
    try {
      debugPrint('Setting up chat messages stream for request: $requestId');

      // Use a broadcast controller to avoid "Stream has already been listened to" errors
      final StreamController<List<ChatMessageModel>> controller =
          StreamController<List<ChatMessageModel>>.broadcast();

      // Track if controller is closed to prevent adding events to a closed controller
      bool isControllerClosed = false;

      // Set up listener for Firestore
      final firestoreSubscription = _firestore
          .collection('service_requests')
          .doc(requestId)
          .collection('messages')
          .orderBy('created_at', descending: false)
          .snapshots()
          .listen(
            (snapshot) {
              if (isControllerClosed) return;

              try {
                debugPrint(
                  'Received Firestore snapshot with ${snapshot.docs.length} messages',
                );
                // Convert Firestore messages to ChatMessageModel objects
                final firestoreMessages =
                    snapshot.docs
                        .map((doc) => ChatMessageModel.fromFirestore(doc))
                        .toList();

                // Store Firestore messages to merge with Realtime DB messages later
                _processMergedMessages(
                  firestoreMessages,
                  requestId,
                  controller,
                  isControllerClosed,
                );
              } catch (e) {
                debugPrint('Error processing Firestore messages: $e');
              }
            },
            onError: (error) {
              debugPrint('Error in Firestore stream: $error');
              if (!isControllerClosed) {
                controller.addError(error);
              }
            },
          );

      // Set up a separate listener for Realtime Database
      final realtimeSubscription = _realtimeDb
          .ref()
          .child('messages')
          .child(requestId)
          .onValue
          .listen(
            (event) {
              if (isControllerClosed) return;

              try {
                debugPrint('Received Realtime DB snapshot for messages');
                final List<ChatMessageModel> realtimeMessages = [];

                if (event.snapshot.value != null) {
                  try {
                    Map<dynamic, dynamic> messagesMap =
                        Map<dynamic, dynamic>.from(event.snapshot.value as Map);
                    debugPrint(
                      'Realtime DB has ${messagesMap.length} messages for request $requestId',
                    );
                    messagesMap.forEach((key, value) {
                      try {
                        if (value is Map) {
                          Map<String, dynamic> messageData =
                              Map<String, dynamic>.from(value);
                          messageData['id'] = key;
                          realtimeMessages.add(
                            ChatMessageModel.fromRealtime(messageData),
                          );
                        }
                      } catch (e) {
                        debugPrint('Error parsing individual message: $e');
                      }
                    });
                  } catch (e) {
                    debugPrint('Error parsing Realtime DB messages: $e');
                  }
                } else {
                  debugPrint(
                    'Realtime DB snapshot value is null for request $requestId',
                  );
                }

                debugPrint(
                  'Processed ${realtimeMessages.length} messages from Realtime DB',
                );

                // Force merge immediately with any stored Firestore messages
                _processMergedMessages(
                  realtimeMessages,
                  requestId,
                  controller,
                  isControllerClosed,
                );
              } catch (e) {
                debugPrint('Error processing Realtime DB messages: $e');
              }
            },
            onError: (error) {
              debugPrint('Error in Realtime DB stream: $error');
              if (!isControllerClosed) {
                controller.addError(error);
              }
            },
          );

      // Handle controller closure
      controller.onCancel = () {
        isControllerClosed = true;
        firestoreSubscription.cancel();
        realtimeSubscription.cancel();
      };

      return controller.stream;
    } catch (e) {
      debugPrint('Error setting up chat message stream: $e');
      return Stream.value([]);
    }
  }

  // Helper method to merge and deduplicate messages
  void _processMergedMessages(
    List<ChatMessageModel> newMessages,
    String requestId,
    StreamController<List<ChatMessageModel>> controller,
    bool isControllerClosed,
  ) async {
    if (isControllerClosed) return;

    try {
      // Get all messages from both sources
      final List<ChatMessageModel> allMessages = [...newMessages];

      // Deduplicate messages by both ID and content+timestamp
      final uniqueMessages = <ChatMessageModel>[];
      final messageIds = <String>{};
      final messageSignatures = <String>{};

      // First pass: add messages with unique IDs and content+timestamp signatures
      for (final message in allMessages) {
        // Create a signature using content, sender, and timestamp
        // This helps catch duplicate messages even if they have different IDs
        final signature =
            '${message.content}_${message.senderId}_${message.createdAt.millisecondsSinceEpoch}';

        if (message.id.isNotEmpty &&
            !messageIds.contains(message.id) &&
            !messageSignatures.contains(signature)) {
          messageIds.add(message.id);
          messageSignatures.add(signature);
          uniqueMessages.add(message);
          // Log message type for debugging
          debugPrint(
            'Message sender type: ${message.senderType}, current user: ${message.senderId == _auth.currentUser?.uid}, system: ${message.messageType == MessageType.system}',
          );
        }
      }

      // Sort by timestamp
      uniqueMessages.sort((a, b) => a.createdAt.compareTo(b.createdAt));

      debugPrint(
        'Combined stream returning ${uniqueMessages.length} unique messages',
      );

      // Add to the controller if not closed
      if (!isControllerClosed) {
        controller.add(uniqueMessages);
      }
    } catch (e) {
      debugPrint('Error processing merged messages: $e');
      if (!isControllerClosed) {
        controller.addError(e);
      }
    }
  }

  /// Check if chat is active for a request
  Future<bool> isChatActive(String requestId) async {
    try {
      final docRef = _serviceRequestsCollection.doc(requestId);
      final doc = await docRef.get();

      if (!doc.exists) {
        return false;
      }

      final data = doc.data() as Map<String, dynamic>;

      // Check for both camelCase and snake_case keys
      return data['chatActive'] == true || data['chat_active'] == true;
    } catch (e) {
      debugPrint('Error checking if chat is active: $e');
      return false;
    }
  }

  /// Check if chat was forcibly closed by technician
  Future<bool> checkIfChatForceClosed(String requestId) async {
    try {
      debugPrint('Checking if chat was force closed for request: $requestId');

      // Check Firestore document
      final docRef = _serviceRequestsCollection.doc(requestId);
      final doc = await docRef.get();

      if (!doc.exists) {
        debugPrint('Request document not found');
        return false;
      }

      final data = doc.data() as Map<String, dynamic>;

      // Check for chat force closed flag
      final forceClosed =
          data['force_closed'] == true ||
          data['forceClosed'] == true ||
          data['chat_force_closed'] == true ||
          data['chatForceClosed'] == true;

      debugPrint('Force closed status: $forceClosed');
      return forceClosed;
    } catch (e) {
      debugPrint('Error checking if chat was force closed: $e');
      return false;
    }
  }

  /// Get a stream of chat active status changes
  Stream<bool> getChatActiveStream(String requestId) {
    try {
      return _serviceRequestsCollection.doc(requestId).snapshots().map((
        snapshot,
      ) {
        if (!snapshot.exists) {
          return false;
        }

        final data = snapshot.data() as Map<String, dynamic>;

        // Check both snake_case and camelCase fields for compatibility
        return data['chatActive'] == true || data['chat_active'] == true;
      });
    } catch (e) {
      debugPrint('Error setting up chat active stream: $e');
      // Return a stream with a single value of false in case of error
      return Stream.value(false);
    }
  }

  /// Update chat status for a request - update both properties
  Future<void> updateRequestChatStatus(String requestId, bool isActive) async {
    try {
      // Update in Firestore with both properties
      await _serviceRequestsCollection.doc(requestId).update({
        'chat_active': isActive,
        'chatActive': isActive,
        'has_active_chat': isActive,
        'updated_at': FieldValue.serverTimestamp(),
      });

      // Update in Realtime Database for faster access
      await _realtimeDb.ref().child('requests_meta').child(requestId).update({
        'chat_active': isActive,
        'chatActive': isActive,
        'has_active_chat': isActive,
        'updated_at': ServerValue.timestamp,
      });

      debugPrint('Chat status updated successfully to $isActive');
    } catch (e) {
      debugPrint('Error updating chat status: $e');
      throw Exception('Failed to update chat status: $e');
    }
  }

  /// Mark all messages as read for a specific request
  Future<void> markMessagesAsRead(String requestId) async {
    try {
      // Get current user ID
      final String userId = _auth.currentUser?.uid ?? '';
      if (userId.isEmpty) throw Exception('User not authenticated');

      debugPrint(
        'Marking messages as read for request $requestId by user $userId',
      );

      bool firestoreSuccess = false;
      bool rtdbSuccess = false;

      // First, try to get messages from Firestore that need to be marked as read
      try {
        final firestoreMessagesQuery =
            await _firestore
                .collection('service_requests')
                .doc(requestId)
                .collection('messages')
                .where('read', isEqualTo: false)
                .where('sender_id', isNotEqualTo: userId)
                .get();

        if (firestoreMessagesQuery.docs.isNotEmpty) {
          debugPrint(
            'Found ${firestoreMessagesQuery.docs.length} unread Firestore messages',
          );

          // Update each Firestore message
          for (final doc in firestoreMessagesQuery.docs) {
            try {
              await doc.reference.update({'read': true, 'is_read': true});
              debugPrint('Marked Firestore message ${doc.id} as read');
            } catch (updateError) {
              debugPrint(
                'Error updating Firestore message ${doc.id}: $updateError',
              );
            }
          }

          firestoreSuccess = true;
        } else {
          debugPrint('No unread Firestore messages found');
          firestoreSuccess = true; // No messages to update is still a success
        }
      } catch (firestoreError) {
        debugPrint('Error accessing Firestore messages: $firestoreError');
        // Continue with RTDB even if Firestore fails
      }

      // Now try to get messages from Realtime Database
      try {
        final DatabaseReference messagesRef = _realtimeDb
            .ref()
            .child('messages')
            .child(requestId);
        final DataSnapshot snapshot = await messagesRef.get();

        if (snapshot.value != null) {
          final Map<dynamic, dynamic> messagesMap = Map<dynamic, dynamic>.from(
            snapshot.value as Map,
          );

          if (messagesMap.isNotEmpty) {
            debugPrint(
              'Found ${messagesMap.length} RTDB messages, checking for unread ones',
            );

            // Batch updates for better performance
            final Map<String, Object?> updates = {};

            messagesMap.forEach((key, value) {
              if (value is Map) {
                final Map<String, dynamic> messageData =
                    Map<String, dynamic>.from(value);

                // Only mark messages from the other user as read
                if (messageData['sender_id'] != userId &&
                    (messageData['read'] == false ||
                        messageData['is_read'] == false)) {
                  updates['/messages/$requestId/$key/read'] = true;
                  updates['/messages/$requestId/$key/is_read'] = true;
                }
              }
            });

            // Apply all updates at once if there are any
            if (updates.isNotEmpty) {
              debugPrint(
                'Marking ${updates.length} Realtime DB messages as read',
              );
              try {
                await _realtimeDb.ref().update(updates);
                rtdbSuccess = true;
                debugPrint('Successfully marked RTDB messages as read');
              } catch (updateError) {
                debugPrint('Error updating RTDB messages: $updateError');
                if (updateError.toString().contains('PERMISSION_DENIED')) {
                  debugPrint(
                    'Permission denied. Check Firebase Realtime Database rules.',
                  );
                }
              }
            } else {
              debugPrint('No unread RTDB messages found');
              rtdbSuccess = true; // No messages to update is still a success
            }
          } else {
            debugPrint('No messages found in RTDB');
            rtdbSuccess = true; // No messages at all is still a success
          }
        } else {
          debugPrint('No messages found in RTDB (null value)');
          rtdbSuccess = true; // No messages at all is still a success
        }
      } catch (rtdbError) {
        debugPrint('Error accessing RTDB messages: $rtdbError');
      }

      // Log overall success/failure
      if (firestoreSuccess || rtdbSuccess) {
        debugPrint(
          'Messages marked as read successfully in at least one database',
        );
      } else {
        debugPrint('Failed to mark messages as read in both databases');
      }
    } catch (e) {
      debugPrint('Error in markMessagesAsRead function: $e');
      // Don't throw as this might break the UI - just log the error
    }
  }

  // Get all chat messages for a request (one-time fetch)
  Future<List<ChatMessageModel>> getChatMessages(String requestId) async {
    try {
      final messagesSnapshot =
          await _firestore
              .collection('service_requests')
              .doc(requestId)
              .collection('messages')
              .orderBy('created_at', descending: false)
              .get();

      return messagesSnapshot.docs
          .map((doc) => ChatMessageModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting chat messages: $e');
      rethrow;
    }
  }

  // Get active chats for a user
  Future<List<RequestModel>> getActiveChatsForUser(String userId) async {
    try {
      // Get requests where the user is a customer and chat is active
      final querySnapshot =
          await _serviceRequestsCollection
              .where('customer_id', isEqualTo: userId)
              .where('chat_active', isEqualTo: true)
              .orderBy('updated_at', descending: true)
              .get();

      return querySnapshot.docs
          .map(
            (doc) => RequestModel.fromMap(
              doc.id,
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('Error getting active chats for user: $e');
      return [];
    }
  }

  // Update a specific chat message
  Future<void> updateChatMessage(
    String requestId,
    String messageId,
    Map<String, dynamic> data,
  ) async {
    try {
      debugPrint(
        'Updating chat message: $messageId in request: $requestId with data: $data',
      );

      // First, try to update in Firestore
      try {
        await _firestore
            .collection('service_requests')
            .doc(requestId)
            .collection('messages')
            .doc(messageId)
            .update(data);
        debugPrint('Chat message updated in Firestore: $messageId');
      } catch (firestoreError) {
        debugPrint(
          'Error updating message in Firestore (continuing to RTDB): $firestoreError',
        );
        // Continue to try Realtime Database even if Firestore fails
      }

      // Then try to update in Realtime Database
      try {
        await _realtimeDb
            .ref()
            .child('messages')
            .child(requestId)
            .child(messageId)
            .update(data);
        debugPrint('Chat message updated in Realtime Database: $messageId');
      } catch (rtdbError) {
        debugPrint('Error updating message in Realtime Database: $rtdbError');
        // If both updates fail, throw an error
        if (rtdbError.toString().contains('PERMISSION_DENIED')) {
          debugPrint(
            'Permission denied for Realtime Database. Check if you are authenticated and have proper permissions.',
          );
        }
      }

      // If we made it here, at least one of the updates succeeded
    } catch (e) {
      debugPrint('Error updating chat message: $e');
      // Don't rethrow as this might break the UI if permissions are incomplete
      // Instead, log the error and continue
    }
  }

  // Batch write helper to optimize Firestore costs
  Future<void> batchWrite({
    List<Map<String, dynamic>> creates = const [],
    List<Map<String, dynamic>> updates = const [],
    List<String> deletes = const [],
  }) async {
    try {
      // Firestore allows a maximum of 500 operations in a single batch
      const int maxBatchSize = 500;
      int totalOperations = creates.length + updates.length + deletes.length;

      if (totalOperations == 0) {
        debugPrint('No operations to perform in batch');
        return;
      }

      // Log batch size for debugging
      debugPrint('Performing batch write with $totalOperations operations');
      debugPrint('- Creates: ${creates.length}');
      debugPrint('- Updates: ${updates.length}');
      debugPrint('- Deletes: ${deletes.length}');

      // If we have more than maxBatchSize operations, we need to split them
      if (totalOperations > maxBatchSize) {
        // Split creates
        for (int i = 0; i < creates.length; i += maxBatchSize) {
          int end =
              i + maxBatchSize > creates.length
                  ? creates.length
                  : i + maxBatchSize;
          await batchWrite(creates: creates.sublist(i, end));
        }

        // Split updates
        for (int i = 0; i < updates.length; i += maxBatchSize) {
          int end =
              i + maxBatchSize > updates.length
                  ? updates.length
                  : i + maxBatchSize;
          await batchWrite(updates: updates.sublist(i, end));
        }

        // Split deletes
        for (int i = 0; i < deletes.length; i += maxBatchSize) {
          int end =
              i + maxBatchSize > deletes.length
                  ? deletes.length
                  : i + maxBatchSize;
          await batchWrite(deletes: deletes.sublist(i, end));
        }

        return;
      }

      // Create a new batch
      final batch = _firestore.batch();

      // Add creates to batch
      for (final create in creates) {
        final collectionPath = create['collection'] as String;
        final data = create['data'] as Map<String, dynamic>;
        final id = create['id'] as String?;

        final collection = _firestore.collection(collectionPath);
        final doc = id != null ? collection.doc(id) : collection.doc();

        batch.set(doc, data);
      }

      // Add updates to batch
      for (final update in updates) {
        final collectionPath = update['collection'] as String;
        final id = update['id'] as String;
        final data = update['data'] as Map<String, dynamic>;
        final merge = update['merge'] as bool? ?? true;

        final doc = _firestore.collection(collectionPath).doc(id);

        if (merge) {
          batch.set(doc, data, SetOptions(merge: true));
        } else {
          batch.update(doc, data);
        }
      }

      // Add deletes to batch
      for (final path in deletes) {
        // Path format: collection/docId
        final parts = path.split('/');
        if (parts.length != 2) {
          debugPrint(
            'Invalid delete path: $path. Should be in format collection/docId',
          );
          continue;
        }

        final collection = parts[0];
        final docId = parts[1];

        batch.delete(_firestore.collection(collection).doc(docId));
      }

      // Commit the batch
      await batch.commit();
      debugPrint('Batch committed successfully');

      // Consider invalidating relevant caches
      if (updates.any((u) => u['collection'] == 'services')) {
        invalidateCache('all_services');
      }

      // Invalidate user caches
      for (final update in updates) {
        if (update['collection'] == 'users') {
          invalidateCache('user_${update['id']}');
        }
      }

      // Invalidate request caches
      for (final update in updates) {
        if (update['collection'] == 'service_requests') {
          invalidateCache('request_${update['id']}');
        }
      }
    } catch (e) {
      debugPrint('Error performing batch write: $e');
      rethrow;
    }
  }

  // Example of using batch update for multiple operations
  Future<void> updateUserAndRequests({
    required String userId,
    required Map<String, dynamic> userData,
    required List<String> requestIds,
    required Map<String, dynamic> requestUpdates,
  }) async {
    try {
      final updates = [
        {'collection': 'users', 'id': userId, 'data': userData, 'merge': true},
        ...requestIds.map(
          (requestId) => {
            'collection': 'service_requests',
            'id': requestId,
            'data': requestUpdates,
            'merge': true,
          },
        ),
      ];

      await batchWrite(updates: updates);

      // Clear cache for affected documents
      invalidateCache('user_$userId');
      for (final requestId in requestIds) {
        invalidateCache('request_$requestId');
      }
    } catch (e) {
      debugPrint('Error updating user and requests: $e');
      rethrow;
    }
  }
}
