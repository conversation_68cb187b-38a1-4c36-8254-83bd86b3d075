import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../services/secure_storage_service.dart';

/// A utility class for handling environment variables in the app
/// Uses flutter_dotenv to load variables from .env file
/// Sensitive data is stored in secure storage
class EnvConfig {
  static EnvConfig? _instance;
  Map<String, String> _envVars = {};
  bool _isInitialized = false;
  final _secureStorage = SecureStorageService();

  // List of sensitive keys that should be stored in secure storage
  static const List<String> _sensitiveKeys = [
    'PAYMOB_API_KEY',
    'PAYMOB_INTEGRATION_ID',
    'PAYMOB_PUBLIC_KEY',
    'FIREBASE_API_KEY',
    'GOOGLE_API_KEY',
  ];

  // Environment keys that should be stored securely
  static const List<String> _secureKeys = [
    'PAYMOB_API_KEY',
    'PAYMOB_INTEGRATION_ID',
  ];

  /// Private constructor for singleton
  EnvConfig._();

  /// Get singleton instance
  static EnvConfig get instance {
    _instance ??= EnvConfig._();
    return _instance!;
  }

  /// Check if config is initialized
  bool get isInitialized => _isInitialized;

  /// Initialize environment variables from .env file
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load .env file using flutter_dotenv
      await dotenv.load(fileName: '.env');

      // Copy non-sensitive values to our internal map
      _envVars = Map.from(dotenv.env);

      // Move sensitive values to secure storage
      await _storeSensitiveValues();

      // Add compile-time environment variables if any
      await _addDartDefines();

      _isInitialized = true;

      // Debug log the loaded env vars for Paymob (without showing actual values)
      if (kDebugMode) {
        await _debugPrintEnvironmentStatus();
      }

      debugPrint('Environment configuration loaded');
    } catch (e) {
      debugPrint('Error initializing environment configuration: $e');
      // Initialize with empty map to avoid null checks
      _envVars = {};
      _isInitialized = true;
    }
  }

  /// Store sensitive values in secure storage
  Future<void> _storeSensitiveValues() async {
    for (final key in _sensitiveKeys) {
      final value = dotenv.env[key];
      if (value != null && value.isNotEmpty) {
        // Store in secure storage
        await _secureStorage.storeData(key, value);

        // Remove from in-memory map for security
        _envVars.remove(key);
      }
    }
  }

  /// Debug print environment status
  Future<void> _debugPrintEnvironmentStatus() async {
    debugPrint('\n======= ENV CONFIG STATUS =======');
    debugPrint(
      'Total environment variables loaded: ${_envVars.length + _sensitiveKeys.length}',
    );

    // Debug: Check if important keys are loaded (without revealing values)
    final hasApiKey = await _checkSecureKeyExists('PAYMOB_API_KEY');
    final hasIntegrationId = await _checkSecureKeyExists(
      'PAYMOB_INTEGRATION_ID',
    );

    debugPrint(
      'PAYMOB_API_KEY: ${hasApiKey ? "✓ LOADED (SECURE)" : "✗ MISSING"}',
    );
    debugPrint(
      'PAYMOB_INTEGRATION_ID: ${hasIntegrationId ? "✓ LOADED (SECURE)" : "✗ MISSING"}',
    );

    // Print .env file loading path
    debugPrint(
      'ENV_FILE_LOCATION: ${dotenv.env['DOTENV_PATH'] ?? File('.env').absolute.path}',
    );
    debugPrint('================================\n');
  }

  /// Check if a secure key exists
  Future<bool> _checkSecureKeyExists(String key) async {
    final value = await _secureStorage.getData(key);
    return value != null && value.isNotEmpty;
  }

  /// Add environment variables from dart defines (--dart-define)
  Future<void> _addDartDefines() async {
    // These are passed at build time with --dart-define
    // Store common environment variables
    final paymobApiKey = const String.fromEnvironment('PAYMOB_API_KEY');
    final paymobIntegrationId = const String.fromEnvironment(
      'PAYMOB_INTEGRATION_ID',
    );

    if (paymobApiKey.isNotEmpty) {
      await _secureStorage.storeData('PAYMOB_API_KEY', paymobApiKey);
      debugPrint('Loaded PAYMOB_API_KEY from dart define (SECURE)');
    }
    if (paymobIntegrationId.isNotEmpty) {
      await _secureStorage.storeData(
        'PAYMOB_INTEGRATION_ID',
        paymobIntegrationId,
      );
      debugPrint('Loaded PAYMOB_INTEGRATION_ID from dart define (SECURE)');
    }
  }

  /// Get environment variable by key
  Future<String?> get(String key) async {
    if (!_isInitialized) {
      debugPrint('Warning: EnvConfig not initialized before use');
      return null;
    }

    // Check if this is a sensitive key that should be in secure storage
    if (_sensitiveKeys.contains(key)) {
      return await _secureStorage.getData(key);
    }

    // Otherwise check in our cached map
    final value = _envVars[key];

    // If not found, check directly in dotenv
    final dotenvValue = value ?? dotenv.env[key];

    if (dotenvValue == null && key.contains('PAYMOB')) {
      // Provide a helpful warning only for Paymob-related keys
      debugPrint('❌ Missing environment variable: $key');
      debugPrint('💡 Make sure to:');
      debugPrint('   1. Create a .env file with $key in the project root, or');
      debugPrint('   2. Build with --dart-define=$key=your_value_here');
    }

    return dotenvValue;
  }

  /// Get environment variable by key with default value
  Future<String> getOrDefault(String key, String defaultValue) async {
    return (await get(key)) ?? defaultValue;
  }

  /// Return all non-sensitive environment variables (for debugging)
  Map<String, String> getNonSensitiveVariables() {
    return Map.from(_envVars);
  }
}
