import { type MenuItem, Permission } from '../types/permissions';
import {
  LayoutDashboard,
  Users,
  FileText,
  Settings,
  MessageSquare,
  CreditCard,
  Wrench,
  <PERSON><PERSON><PERSON>,
  <PERSON>r<PERSON><PERSON><PERSON>,
  UserCog,
} from 'lucide-react';

export const navigationItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'LayoutDashboard',
    path: '/dashboard',
    permissions: [Permission.VIEW_DASHBOARD],
  },
  {
    id: 'requests',
    label: 'Requests',
    icon: 'FileText',
    path: '/requests',
    permissions: [Permission.VIEW_ALL_REQUESTS, Permission.VIEW_OWN_REQUESTS],
  },
  {
    id: 'technicians',
    label: 'Technicians',
    icon: 'UserCheck',
    path: '/technicians',
    permissions: [Permission.VIEW_TECHNICIANS],
  },
  {
    id: 'users',
    label: 'Users',
    icon: 'Users',
    path: '/users',
    permissions: [Permission.VIEW_USERS],
  },
  {
    id: 'services',
    label: 'Services',
    icon: 'Wrench',
    path: '/services',
    permissions: [Permission.VIEW_SERVICES],
  },
  {
    id: 'chat',
    label: 'Chat',
    icon: 'MessageSquare',
    path: '/chat',
    permissions: [Permission.VIEW_ALL_CHATS, Permission.VIEW_OWN_CHATS],
  },
  {
    id: 'payments',
    label: 'Payments',
    icon: 'CreditCard',
    path: '/payments',
    permissions: [Permission.VIEW_PAYMENTS],
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: 'BarChart',
    path: '/reports',
    permissions: [Permission.VIEW_REPORTS],
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: 'Settings',
    path: '/settings',
    permissions: [Permission.VIEW_SETTINGS],
  },
  {
    id: 'account',
    label: 'Account',
    icon: 'UserCog',
    path: '/account',
    permissions: [Permission.VIEW_DASHBOARD], // All authenticated users can access their account
  },
];

// Icon mapping for dynamic rendering
export const iconMap = {
  LayoutDashboard,
  Users,
  FileText,
  Settings,
  MessageSquare,
  CreditCard,
  Wrench,
  BarChart,
  UserCheck,
  UserCog,
};

// Get icon component by name
export const getIcon = (iconName?: string) => {
  if (!iconName) return null;
  return iconMap[iconName as keyof typeof iconMap] || null;
}; 