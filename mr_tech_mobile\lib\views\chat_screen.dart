import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import '../models/chat_message_model.dart';
import '../models/request_model.dart';
import '../services/chat_service.dart';
import '../services/database_service.dart';
import '../services/translation_service.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/network_image_handler.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../widgets/text_styles.dart';
import '../widgets/ui_kit.dart';

// Add this enum at the top of the file, outside any class
enum ChatStatus { active, waiting, disabled, error }

class ChatScreen extends StatefulWidget {
  final RequestModel request;

  const ChatScreen({super.key, required this.request});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final ChatService _chatService = ChatService();
  final DatabaseService _databaseService = DatabaseService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ImagePicker _imagePicker = ImagePicker();

  bool _isSending = false;
  bool _isUploading = false;
  bool _isLoading = true;
  final bool _isChatActive = false;
  List<ChatMessageModel> _messages = [];
  File? _selectedFile;
  String? _selectedFileName;
  StreamSubscription<List<ChatMessageModel>>? _messageSubscription;
  Timer? _refreshTimer;
  ChatStatus _chatStatus = ChatStatus.waiting;
  String _statusMessage = '';
  StreamSubscription<bool>? _chatStatusSubscription;
  RequestModel?
  _currentRequest; // Store current request data (may be updated from DB)

  // Get current user ID from Firebase Auth
  String? get _currentUserId => FirebaseAuth.instance.currentUser?.uid;

  @override
  void initState() {
    super.initState();
    _currentRequest = widget.request; // Initialize with widget data
    _initializeChat();
  }

  Future<void> _initializeChat() async {
    try {
      setState(() => _isLoading = true);

      // Get current user ID from Firebase Auth
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) {
        debugPrint('ChatScreen: User not authenticated');
        throw Exception('User not authenticated');
      }

      debugPrint(
        'ChatScreen: Initializing chat for request ${widget.request.id}',
      );
      debugPrint(
        'ChatScreen: Request object status: ${widget.request.status}, chatActive: ${widget.request.chatActive}',
      );

      // Refresh request data from database to get latest technician info
      try {
        final updatedRequest = await _databaseService.getRequestById(
          widget.request.id,
          forceRefresh: true,
        );
        if (updatedRequest != null) {
          debugPrint(
            'ChatScreen: Updated request data - Technician ID: ${updatedRequest.technicianId}, Name: ${updatedRequest.technicianName}',
          );
          // Update our current request data with the latest from DB
          setState(() {
            _currentRequest = updatedRequest;
          });

          if (updatedRequest.technicianName != null &&
              updatedRequest.technicianName!.isNotEmpty) {
            debugPrint(
              'ChatScreen: Found technician name in database: ${updatedRequest.technicianName}',
            );
          } else {
            debugPrint('ChatScreen: No technician name found in database');
          }
        }
      } catch (e) {
        debugPrint('ChatScreen: Error refreshing request data: $e');
        // Continue with existing data
      }

      // Set current user ID in chat service
      _chatService.setCurrentUserId(userId);

      // First, check for any pending chat activation notifications for this request
      debugPrint('ChatScreen: Checking for chat activation notifications');
      try {
        final notificationQuery =
            await FirebaseFirestore.instance
                .collection('notifications')
                .where('user_id', isEqualTo: userId)
                .where('request_id', isEqualTo: widget.request.id)
                .where(
                  'type',
                  whereIn: [
                    'CHAT_ACTIVATION',
                    'chat_activation',
                    'CHAT_STARTED',
                    'chat_started',
                  ],
                )
                .orderBy('timestamp', descending: true)
                .limit(1)
                .get();

        if (notificationQuery.docs.isNotEmpty) {
          final notificationData = notificationQuery.docs.first.data();
          debugPrint(
            'ChatScreen: Found chat activation notification, ID: ${notificationQuery.docs.first.id}',
          );
          debugPrint(
            'ChatScreen: Notification data: ${notificationData['type']} sent at ${notificationData['timestamp']}',
          );

          // Mark notification as processed
          try {
            await notificationQuery.docs.first.reference.update({
              'delivered': true,
              'processed_by_app': true,
              'opened_in_chat': true,
              'read': true,
              'processed': true,
            });
            debugPrint('ChatScreen: Marked notification as processed');
          } catch (updateError) {
            // Log but don't let it stop initialization
            debugPrint(
              'ChatScreen: Error marking notification as processed: $updateError',
            );
            debugPrint(
              'ChatScreen: Continuing with chat initialization regardless of notification update error',
            );
          }

          // Force update request chat status to active
          try {
            await FirebaseFirestore.instance
                .collection('service_requests')
                .doc(widget.request.id)
                .update({
                  'chatActive': true,
                  'chat_active': true,
                  'updated_at': FieldValue.serverTimestamp(),
                  'last_activity': FieldValue.serverTimestamp(),
                });
            debugPrint(
              'ChatScreen: Updated request chat status to active based on notification',
            );
          } catch (e) {
            debugPrint('ChatScreen: Error updating request chat status: $e');
          }
        } else {
          debugPrint(
            'ChatScreen: No chat activation notifications found for request ${widget.request.id}',
          );
        }
      } catch (e) {
        debugPrint(
          'ChatScreen: Error checking for chat activation notifications: $e',
        );
        // Continue with normal flow
      }

      // Check if chat is active by querying DB directly
      final isActive = await _databaseService.isChatActive(widget.request.id);
      debugPrint('ChatScreen: Chat active status from DB check: $isActive');

      // Check if request is completed
      if (widget.request.status == RequestStatus.completed) {
        debugPrint('ChatScreen: Request is completed, chat may be disabled');
        final forceClosedFlag = await _databaseService.checkIfChatForceClosed(
          widget.request.id,
        );
        if (forceClosedFlag) {
          debugPrint('ChatScreen: Chat was force closed by technician');
          setState(() {
            _chatStatus = ChatStatus.disabled;
            _statusMessage = 'This chat has been closed by the technician.';
            _isLoading = false;
          });
          return;
        }
      }

      // If chat is active, load messages
      if (isActive || widget.request.chatActive == true) {
        debugPrint('ChatScreen: Chat is active, setting up chat listener');
        // Set up listener
        _chatService.setupChatListener(widget.request.id);

        // Fetch existing messages
        await _fetchMessages();

        setState(() {
          _chatStatus = ChatStatus.active;
          _isLoading = false;
        });
      } else {
        debugPrint('ChatScreen: Chat is not active, waiting for technician');
        setState(() {
          _chatStatus = ChatStatus.waiting;
          _statusMessage = 'Waiting for the technician to start the chat.';
          _isLoading = false;
        });

        // Set up a listener to detect when chat becomes active
        _setupChatStatusListener();
      }
    } catch (e) {
      debugPrint('ChatScreen: Error initializing chat: $e');
      setState(() {
        _chatStatus = ChatStatus.error;
        _statusMessage = 'Failed to initialize chat. Please try again.';
        _isLoading = false;
      });
    }
  }

  // Set up message stream for real-time updates
  void _setupMessageStream() {
    try {
      debugPrint('ChatScreen: Setting up message stream');
      // Cancel any existing subscription first
      _messageSubscription?.cancel();

      // Cancel existing timer if any
      _refreshTimer?.cancel();

      // Listen to messages for this chat
      _chatService.setupChatListener(widget.request.id);

      // This subscription is for the UI display only
      _messageSubscription = _chatService
          .getMessagesStream(widget.request.id)
          .listen(
            (messages) {
              if (mounted) {
                // Only update state if messages are different - prevent unnecessary rebuilds
                if (_messagesAreDifferent(messages, _messages)) {
                  setState(() {
                    _messages = messages;
                  });

                  // Scroll to bottom on new messages if near bottom
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _scrollToBottomIfNeeded();
                  });

                  // Mark messages as read automatically
                  _markMessagesAsReadAsync();
                }
              }
            },
            onError: (error) {
              debugPrint('ChatScreen: Error in message stream: $error');
            },
          );
    } catch (e) {
      debugPrint('ChatScreen: Error setting up message stream: $e');
    }
  }

  // Compare two message lists to see if they are different
  bool _messagesAreDifferent(
    List<ChatMessageModel> newMessages,
    List<ChatMessageModel> oldMessages,
  ) {
    // Quick size comparison first
    if (newMessages.length != oldMessages.length) {
      debugPrint(
        'Message count changed: ${oldMessages.length} -> ${newMessages.length}',
      );
      return true;
    }

    // Check if message IDs and content are the same in the same order
    for (int i = 0; i < newMessages.length; i++) {
      if (newMessages[i].id != oldMessages[i].id ||
          newMessages[i].content != oldMessages[i].content ||
          newMessages[i].isRead != oldMessages[i].isRead) {
        debugPrint(
          'Message at index $i changed: ID or content or read status differs',
        );
        return true;
      }
    }

    debugPrint('No change in messages detected, avoiding rebuild');
    return false;
  }

  // Helper method to mark messages as read
  Future<void> _markMessagesAsReadAsync() async {
    try {
      // Don't need to do anything if no messages
      if (_messages.isEmpty) return;

      // Find messages from technician that aren't read
      final unreadMessages =
          _messages
              .where(
                (msg) => msg.senderType == SenderType.technician && !msg.isRead,
              )
              .toList();

      if (unreadMessages.isNotEmpty) {
        await _chatService.markMessagesAsRead(widget.request.id);
      }
    } catch (e) {
      debugPrint('ChatScreen: Error marking messages as read: $e');
    }
  }

  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }

  void _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty && _selectedFile == null) return;

    setState(() => _isSending = true);

    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      if (_selectedFile != null) {
        // Upload file message
        setState(() => _isUploading = true);

        await _chatService.sendFileMessage(
          requestId: widget.request.id,
          file: _selectedFile!,
          caption: message.isNotEmpty ? message : null,
        );

        setState(() {
          _selectedFile = null;
          _selectedFileName = null;
          _isUploading = false;
        });
      } else {
        // Send text message
        await _chatService.sendMessage(
          requestId: widget.request.id,
          content: message,
        );
      }

      _messageController.clear();

      // Scroll to bottom
      _scrollToBottom();
    } catch (e) {
      _showErrorSnackbar('Error sending message: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() => _isSending = false);
      }
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedFile = File(image.path);
          _selectedFileName = path.basename(image.path);
        });
      }
    } catch (e) {
      _showErrorSnackbar('Error picking image: ${e.toString()}');
    }
  }

  Future<void> _pickFile() async {
    try {
      final XFile? file = await _imagePicker.pickMedia();

      if (file != null) {
        setState(() {
          _selectedFile = File(file.path);
          _selectedFileName = path.basename(file.path);
        });
      }
    } catch (e) {
      _showErrorSnackbar('Error picking file: ${e.toString()}');
    }
  }

  void _clearSelectedFile() {
    setState(() {
      _selectedFile = null;
      _selectedFileName = null;
    });
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        final translationService = Provider.of<TranslationService>(
          context,
          listen: false,
        );
        String translate(String key) => translationService.translate(key);

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: Text(translate('Take Photo')),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: Text(translate('Photo Gallery')),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.attach_file),
                title: Text(translate('Document')),
                onTap: () {
                  Navigator.pop(context);
                  _pickFile();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(
      timestamp.year,
      timestamp.month,
      timestamp.day,
    );

    if (messageDate == today) {
      // Today, show time only
      return DateFormat.Hm().format(timestamp);
    } else if (messageDate == today.subtract(const Duration(days: 1))) {
      // Yesterday
      return 'Yesterday ${DateFormat.Hm().format(timestamp)}';
    } else {
      // Other days
      return DateFormat.yMMMd().add_Hm().format(timestamp);
    }
  }

  String _formatDateHeader(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final messageDate = DateTime(
      timestamp.year,
      timestamp.month,
      timestamp.day,
    );

    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );

    if (messageDate == today) {
      return translationService.translate('Today');
    } else if (messageDate == yesterday) {
      return translationService.translate('Yesterday');
    } else {
      return DateFormat.yMMMd().format(timestamp);
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      // Since we're no longer using reverse: true, we scroll to the bottom (maxScrollExtent)
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  bool _isConsecutive(ChatMessageModel current, ChatMessageModel? previous) {
    if (previous == null) return false;

    return current.senderType == previous.senderType &&
        current.senderId == previous.senderId &&
        current.createdAt.difference(previous.createdAt).inMinutes < 2;
  }

  // Group messages by date
  List<Map<String, dynamic>> _groupMessagesByDate() {
    final groups = <Map<String, dynamic>>[];

    for (int i = 0; i < _messages.length; i++) {
      final message = _messages[i];
      final date = _formatDateHeader(message.createdAt);

      if (groups.isEmpty || groups.last['date'] != date) {
        groups.add({
          'date': date,
          'messages': [message],
        });
      } else {
        groups.last['messages'].add(message);
      }
    }

    return groups;
  }

  Future<void> _openFile(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showErrorSnackbar('Could not open the file');
      }
    } catch (e) {
      _showErrorSnackbar('Error opening file: ${e.toString()}');
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageSubscription?.cancel();
    _refreshTimer?.cancel();

    // Ensure chat service properly disposes resources for this request
    _chatService.disposeResources(widget.request.id);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(context);
    final isRtl = translationService.isRtl;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        title: Row(
          children: [
            // Technician Avatar
            if (_currentRequest?.technicianName != null)
              Container(
                margin: const EdgeInsets.only(right: 12),
                child: CircleAvatar(
                  radius: 18,
                  backgroundColor: colorScheme.primary.withOpacity(0.1),
                  child: Icon(
                    Icons.person,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                ),
              ),
            // Technician Name and Status
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _currentRequest?.technicianName ?? _translate('Technician'),
                    style: AppTextStyles.headingSmall(
                      context,
                      color: colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (_chatStatus == ChatStatus.active)
                    Text(
                      _translate('Online'),
                      style: AppTextStyles.bodySmall(
                        context,
                        color: Colors.green,
                      ),
                    )
                  else
                    Text(
                      '#REQ-${_currentRequest?.id.substring(0, 4).toUpperCase() ?? 'REQ'}',
                      style: AppTextStyles.bodySmall(
                        context,
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline, color: colorScheme.onSurface),
            onPressed: _showRequestDetails,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Chat status indicator
                  if (_chatStatus != ChatStatus.active) _buildStatusBanner(),

                  // Messages area
                  Expanded(
                    child:
                        _messages.isEmpty
                            ? _buildEmptyChat()
                            : _buildMessagesList(),
                  ),

                  // Selected file preview
                  if (_selectedFile != null) _buildSelectedFilePreview(),

                  // Input area
                  if (_chatStatus == ChatStatus.active) _buildChatInput(),
                ],
              ),
    );
  }

  Widget _buildStatusBanner() {
    final colorScheme = Theme.of(context).colorScheme;

    Color backgroundColor;
    IconData iconData;

    switch (_chatStatus) {
      case ChatStatus.waiting:
        backgroundColor = Colors.orange.shade50;
        iconData = Icons.hourglass_bottom;
        break;
      case ChatStatus.disabled:
        backgroundColor = Colors.red.shade50;
        iconData = Icons.block;
        break;
      case ChatStatus.error:
        backgroundColor = Colors.red.shade50;
        iconData = Icons.error_outline;
        break;
      default:
        backgroundColor = colorScheme.primary.withOpacity(0.1);
        iconData = Icons.info_outline;
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Icon(
            iconData,
            size: 20,
            color:
                _chatStatus == ChatStatus.waiting
                    ? Colors.orange.shade700
                    : _chatStatus == ChatStatus.error ||
                        _chatStatus == ChatStatus.disabled
                    ? Colors.red.shade700
                    : colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _statusMessage,
              style: AppTextStyles.bodyMedium(
                context,
                color:
                    _chatStatus == ChatStatus.waiting
                        ? Colors.orange.shade700
                        : _chatStatus == ChatStatus.error ||
                            _chatStatus == ChatStatus.disabled
                        ? Colors.red.shade700
                        : colorScheme.primary,
              ),
            ),
          ),
          if (_chatStatus == ChatStatus.error)
            TextButton(
              onPressed: () {
                setState(() => _isLoading = true);
                _initializeChat();
              },
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
              ),
              child: Text(_translate('Retry')),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyChat() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Icon(
                Icons.chat_bubble_outline_rounded,
                size: 48,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              _translate('No messages yet'),
              style: AppTextStyles.headingMedium(
                context,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _translate('Start the conversation by sending a message'),
              textAlign: TextAlign.center,
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessagesList() {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(), // Dismiss keyboard on tap
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
        itemCount: _messages.length,
        reverse:
            false, // Display messages in chronological order (oldest to newest)
        itemBuilder: (context, index) {
          final message = _messages[index];
          return _buildMessageBubble(message);
        },
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessageModel message) {
    final bool isCurrentUser = message.senderId == _currentUserId;
    final bool isSystem = message.senderType == SenderType.system;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // For system messages, show in the center with a different style
    if (isSystem) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12.0,
              vertical: 8.0,
            ),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Text(
              message.content,
              style: AppTextStyles.bodySmall(
                context,
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }

    // For user messages (current user or technician)
    return Align(
      alignment: isCurrentUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: EdgeInsets.only(
          top: 4.0,
          bottom: 4.0,
          left: isCurrentUser ? 64.0 : 8.0,
          right: isCurrentUser ? 8.0 : 64.0,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
        decoration: BoxDecoration(
          color: isCurrentUser ? colorScheme.primary : colorScheme.surface,
          borderRadius: BorderRadius.circular(12.0),
          border:
              isCurrentUser ? null : Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Message content
            if (message.messageType == MessageType.text)
              Text(
                message.content,
                style: AppTextStyles.bodyMedium(
                  context,
                  color:
                      isCurrentUser
                          ? colorScheme.onPrimary
                          : colorScheme.onSurface,
                ),
              ),
            if (message.messageType == MessageType.image)
              _buildImageMessage(message),
            if (message.messageType == MessageType.file)
              _buildFileMessage(message, isCurrentUser),

            // Timestamp
            const SizedBox(height: 6.0),
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment:
                  isCurrentUser
                      ? MainAxisAlignment.end
                      : MainAxisAlignment.start,
              children: [
                Text(
                  _formatTimestamp(message.createdAt),
                  style: AppTextStyles.labelSmall(
                    context,
                    color:
                        isCurrentUser
                            ? colorScheme.onPrimary.withOpacity(0.8)
                            : colorScheme.onSurfaceVariant,
                  ),
                ),
                if (isCurrentUser) ...[
                  const SizedBox(width: 4.0),
                  Icon(
                    message.isRead ? Icons.done_all : Icons.done,
                    size: 12.0,
                    color: colorScheme.onPrimary.withOpacity(0.8),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageMessage(ChatMessageModel message) {
    return GestureDetector(
      onTap: () => _openFile(message.fileUrl!),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            NetworkImageWithFallback(
              imageUrl: message.fileUrl,
              width: double.infinity,
              height: 200,
              fit: BoxFit.cover,
              fallbackIcon: Icons.broken_image,
            ),
            if (message.content.isNotEmpty &&
                message.content != path.basename(message.fileUrl!))
              Container(
                width: double.infinity,
                color: Colors.black54,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                child: Text(
                  message.content,
                  style: AppTextStyles.bodySmall(context, color: Colors.white),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileMessage(ChatMessageModel message, bool isCurrentUser) {
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      onTap: () => _openFile(message.fileUrl!),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color:
              isCurrentUser
                  ? colorScheme.onPrimary.withOpacity(0.1)
                  : colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color:
                isCurrentUser
                    ? colorScheme.onPrimary.withOpacity(0.3)
                    : colorScheme.primary.withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.insert_drive_file,
              color:
                  isCurrentUser ? colorScheme.onPrimary : colorScheme.primary,
              size: 20,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                message.content,
                style: AppTextStyles.bodyMedium(
                  context,
                  color:
                      isCurrentUser
                          ? colorScheme.onPrimary
                          : colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Scroll to bottom if user is already near bottom
  void _scrollToBottomIfNeeded() {
    if (_scrollController.hasClients) {
      final position = _scrollController.position;
      final maxScroll = position.maxScrollExtent;
      final currentScroll = position.pixels;
      const threshold = 200.0; // pixels from bottom to trigger auto-scroll

      // Check if user is near the bottom (within threshold)
      if (maxScroll - currentScroll <= threshold) {
        _scrollToBottom();
      }
    }
  }

  // Add this method to fetch messages
  Future<void> _fetchMessages() async {
    try {
      // Set up a subscription to the messages stream
      final messagesStream = _chatService.getMessagesStream(widget.request.id);
      _messageSubscription = messagesStream.listen(
        (updatedMessages) {
          final bool isNewMessage = updatedMessages.length > _messages.length;
          setState(() {
            _messages = updatedMessages;
          });

          // Auto-scroll to bottom for new messages or when initially loading
          if (isNewMessage || _messages.isEmpty) {
            _scrollToBottom();
          } else {
            _scrollToBottomIfNeeded();
          }
        },
        onError: (e) {
          debugPrint('ChatScreen: Error in messages stream: $e');
        },
      );

      // Mark messages as read
      await _chatService.markMessagesAsRead(widget.request.id);
    } catch (e) {
      debugPrint('ChatScreen: Error fetching messages: $e');
    }
  }

  // Add this method to set up a listener for chat status changes
  void _setupChatStatusListener() {
    try {
      // Listen for chat status changes in Firestore
      final chatStatusStream = _databaseService.getChatActiveStream(
        widget.request.id,
      );
      _chatStatusSubscription = chatStatusStream.listen(
        (isActive) {
          if (isActive && _chatStatus != ChatStatus.active) {
            debugPrint('ChatScreen: Chat became active, updating UI');
            setState(() {
              _chatStatus = ChatStatus.active;
            });

            // Start listening for messages
            _fetchMessages();
          }
        },
        onError: (e) {
          debugPrint('ChatScreen: Error in chat status stream: $e');
        },
      );
    } catch (e) {
      debugPrint('ChatScreen: Error setting up chat status listener: $e');
    }
  }

  String _translate(String text) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(text);
  }

  void _showRequestDetails() {
    // Navigate to request details
    Navigator.pushNamed(
      context,
      '/request-details',
      arguments: _currentRequest?.id ?? widget.request.id,
    );
  }

  Widget _buildSelectedFilePreview() {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Icon(Icons.attach_file, size: 16, color: colorScheme.primary),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _selectedFile != null ? path.basename(_selectedFile!.path) : '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurface,
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.close,
              size: 16,
              color: colorScheme.onSurfaceVariant,
            ),
            onPressed: () {
              setState(() {
                _selectedFile = null;
              });
            },
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildChatInput() {
    final translationService = Provider.of<TranslationService>(context);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Attachment button
          Container(
            decoration: BoxDecoration(
              color: colorScheme.primary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                Icons.attach_file,
                color: colorScheme.primary,
                size: 20,
              ),
              onPressed: _isSending ? null : _showAttachmentOptions,
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(),
            ),
          ),
          const SizedBox(width: 12),
          // Text field
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: translationService.translate('Type a message...'),
                hintStyle: AppTextStyles.bodyMedium(
                  context,
                  color: colorScheme.onSurfaceVariant,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: colorScheme.primary, width: 2),
                ),
                filled: true,
                fillColor: colorScheme.surface,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurface,
              ),
              textDirection: translationService.textDirection,
              maxLines: 4,
              minLines: 1,
              enabled: !_isSending,
              onSubmitted: (_) => _sendMessage(),
              textInputAction: TextInputAction.send,
            ),
          ),
          const SizedBox(width: 12),
          // Send button
          _isSending
              ? Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  ),
                ),
              )
              : InkWell(
                onTap: _sendMessage,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.send,
                    color: colorScheme.onPrimary,
                    size: 20,
                  ),
                ),
              ),
        ],
      ),
    );
  }
}
