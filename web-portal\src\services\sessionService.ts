import { 
  setPersistence, 
  browserLocalPersistence, 
  browserSessionPersistence,
  onIdTokenChanged,
  getIdTokenResult,
  signOut,
  type User as FirebaseUser
} from 'firebase/auth';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  deleteDoc, 
  collection, 
  query, 
  where, 
  getDocs, 
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { DEFAULT_SESSION_CONFIG, type Session, type DeviceInfo, type SessionConfig, type StoredSession } from '../types/session';

class SessionService {
  private config: SessionConfig = DEFAULT_SESSION_CONFIG;
  private activityTimer: NodeJS.Timeout | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;
  private lastActivity: Date = new Date();

  // Initialize session management
  async initialize(config?: Partial<SessionConfig>) {
    this.config = { ...DEFAULT_SESSION_CONFIG, ...config };
    this.setupActivityTracking();
    this.setupTokenRefresh();
  }

  // Set persistence based on remember me
  async setPersistence(rememberMe: boolean) {
    const persistence = rememberMe ? browserLocalPersistence : browserSessionPersistence;
    await setPersistence(auth, persistence);
  }

  // Create a new session
  async createSession(user: FirebaseUser, rememberMe: boolean = false): Promise<Session> {
    const deviceInfo = this.getDeviceInfo();
    const duration = rememberMe ? this.config.rememberMeDuration : this.config.sessionDuration;
    const expiresAt = new Date(Date.now() + duration);

    const session: Session = {
      uid: user.uid,
      email: user.email || '',
      role: (await getIdTokenResult(user)).claims.role as string || 'customer',
      expiresAt,
      deviceInfo,
      createdAt: new Date(),
      lastActivity: new Date(),
    };

    // Store session in Firestore
    await setDoc(doc(db, 'sessions', `${user.uid}_${deviceInfo.deviceId}`), {
      ...session,
      expiresAt: Timestamp.fromDate(expiresAt),
      createdAt: serverTimestamp(),
      lastActivity: serverTimestamp(),
    });

    // Store session locally
    this.storeLocalSession({
      token: await user.getIdToken(),
      expiresAt: expiresAt.toISOString(),
      rememberMe,
      deviceId: deviceInfo.deviceId,
    });

    return session;
  }

  // Get device information
  private getDeviceInfo(): DeviceInfo {
    const userAgent = navigator.userAgent;
    const platform = navigator.platform;
    
    // Simple browser detection
    let browser = 'Unknown';
    if (userAgent.includes('Chrome')) browser = 'Chrome';
    else if (userAgent.includes('Firefox')) browser = 'Firefox';
    else if (userAgent.includes('Safari')) browser = 'Safari';
    else if (userAgent.includes('Edge')) browser = 'Edge';

    // Generate or retrieve device ID
    let deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      deviceId = this.generateDeviceId();
      localStorage.setItem('deviceId', deviceId);
    }

    return {
      userAgent,
      platform,
      browser,
      deviceId,
    };
  }

  // Generate unique device ID
  private generateDeviceId(): string {
    return 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Store session locally
  private storeLocalSession(session: StoredSession) {
    const storage = session.rememberMe ? localStorage : sessionStorage;
    storage.setItem('session', JSON.stringify(session));
  }

  // Get stored session
  getStoredSession(): StoredSession | null {
    const localSession = localStorage.getItem('session');
    const sessionSession = sessionStorage.getItem('session');
    
    if (localSession) {
      return JSON.parse(localSession);
    } else if (sessionSession) {
      return JSON.parse(sessionSession);
    }
    
    return null;
  }

  // Clear stored session
  clearStoredSession() {
    localStorage.removeItem('session');
    sessionStorage.removeItem('session');
  }

  // Setup activity tracking
  private setupActivityTracking() {
    // Track user activity
    const activityEvents = ['mousedown', 'keydown', 'scroll', 'touchstart'];
    
    const updateActivity = () => {
      this.lastActivity = new Date();
      this.checkInactivity();
    };

    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true });
    });

    // Check for inactivity periodically
    this.checkInactivity();
  }

  // Check for inactivity
  private checkInactivity() {
    if (this.activityTimer) {
      clearTimeout(this.activityTimer);
    }

    this.activityTimer = setTimeout(async () => {
      const inactiveTime = Date.now() - this.lastActivity.getTime();
      
      if (inactiveTime >= this.config.inactivityTimeout) {
        // User has been inactive, log them out
        await this.endSession('inactivity');
      } else {
        // Continue checking
        this.checkInactivity();
      }
    }, 60000); // Check every minute
  }

  // Setup token refresh
  private setupTokenRefresh() {
    onIdTokenChanged(auth, async (user) => {
      if (user) {
        const tokenResult = await getIdTokenResult(user);
        const expirationTime = new Date(tokenResult.expirationTime).getTime();
        const now = Date.now();
        const timeUntilExpiry = expirationTime - now;

        // Refresh token 5 minutes before expiry
        const refreshTime = timeUntilExpiry - (5 * 60 * 1000);

        if (this.refreshTimer) {
          clearTimeout(this.refreshTimer);
        }

        if (refreshTime > 0) {
          this.refreshTimer = setTimeout(async () => {
            try {
              await user.getIdToken(true); // Force refresh
              console.log('Token refreshed successfully');
            } catch (error) {
              console.error('Failed to refresh token:', error);
              await this.endSession('token_refresh_failed');
            }
          }, refreshTime);
        }
      }
    });
  }

  // Update session activity
  async updateSessionActivity(uid: string) {
    const deviceInfo = this.getDeviceInfo();
    const sessionRef = doc(db, 'sessions', `${uid}_${deviceInfo.deviceId}`);
    
    try {
      // Check if document exists first
      const docSnap = await getDoc(sessionRef);
      if (docSnap.exists()) {
        await updateDoc(sessionRef, {
          lastActivity: serverTimestamp(),
        });
      } else {
        console.log('Session document does not exist, skipping activity update');
      }
    } catch (error) {
      console.error('Failed to update session activity:', error);
    }
  }

  // Get active sessions for a user
  async getActiveSessions(uid: string): Promise<Session[]> {
    const sessionsQuery = query(
      collection(db, 'sessions'),
      where('uid', '==', uid)
    );

    const querySnapshot = await getDocs(sessionsQuery);
    const sessions: Session[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      sessions.push({
        ...data,
        expiresAt: data.expiresAt.toDate(),
        createdAt: data.createdAt.toDate(),
        lastActivity: data.lastActivity.toDate(),
      } as Session);
    });

    return sessions;
  }

  // End current session
  async endSession(reason: string = 'logout') {
    const user = auth.currentUser;
    if (user) {
      const deviceInfo = this.getDeviceInfo();
      const sessionRef = doc(db, 'sessions', `${user.uid}_${deviceInfo.deviceId}`);
      
      try {
        await deleteDoc(sessionRef);
      } catch (error) {
        console.error('Failed to delete session from Firestore:', error);
      }
    }

    // Clear timers
    if (this.activityTimer) {
      clearTimeout(this.activityTimer);
      this.activityTimer = null;
    }
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    // Clear local storage
    this.clearStoredSession();

    // Sign out from Firebase
    await signOut(auth);

    console.log(`Session ended: ${reason}`);
  }

  // End all sessions for a user
  async endAllSessions(uid: string) {
    const sessionsQuery = query(
      collection(db, 'sessions'),
      where('uid', '==', uid)
    );

    const querySnapshot = await getDocs(sessionsQuery);
    const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
    
    await Promise.all(deletePromises);
  }

  // Check if current session is valid
  async isSessionValid(): Promise<boolean> {
    const storedSession = this.getStoredSession();
    if (!storedSession) return false;

    const expiresAt = new Date(storedSession.expiresAt);
    if (expiresAt <= new Date()) {
      this.clearStoredSession();
      return false;
    }

    return true;
  }

  // Cleanup expired sessions
  async cleanupExpiredSessions() {
    const now = Timestamp.now();
    const sessionsQuery = query(
      collection(db, 'sessions'),
      where('expiresAt', '<=', now)
    );

    const querySnapshot = await getDocs(sessionsQuery);
    const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
    
    await Promise.all(deletePromises);
  }
}

export default new SessionService(); 