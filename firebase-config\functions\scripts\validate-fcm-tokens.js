/**
 * FCM Token Validator Script
 * 
 * This script helps diagnose FCM token issues by:
 * 1. Finding users with inconsistent token storage
 * 2. Validating tokens against Firebase
 * 3. Suggesting migration paths to standardized format
 * 
 * Run with: node validate-fcm-tokens.js
 */

const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

async function validateFcmTokens() {
  console.log('Starting FCM token validation...');
  
  // Get all users
  const usersSnapshot = await db.collection('users').get();
  console.log(`Found ${usersSnapshot.size} user documents`);
  
  const issues = [];
  let validTokenCount = 0;
  let usersWithTokens = 0;
  
  // Check each user
  for (const userDoc of usersSnapshot.docs) {
    const userId = userDoc.id;
    const userData = userDoc.data();
    
    // Track token fields
    const tokenFields = {
      fcm_token: userData.fcm_token || null,
      fcmToken: userData.fcmToken || null,
      fcmTokens: Array.isArray(userData.fcmTokens) ? userData.fcmTokens : [],
      deviceTokens: Array.isArray(userData.deviceTokens) ? userData.deviceTokens : [],
      device_tokens: Array.isArray(userData.device_tokens) ? userData.device_tokens : []
    };
    
    // Check if user has any tokens
    const hasAnyToken = Object.values(tokenFields).some(val => 
      val !== null && (typeof val === 'string' || (Array.isArray(val) && val.length > 0))
    );
    
    if (hasAnyToken) {
      usersWithTokens++;
      
      // Check for inconsistencies
      const tokensFound = Object.entries(tokenFields)
        .filter(([_, value]) => value !== null && (typeof value === 'string' || (Array.isArray(value) && value.length > 0)))
        .map(([key, value]) => ({
          field: key,
          tokens: Array.isArray(value) ? value : [value]
        }));
      
      // If tokens are stored in multiple fields, flag as issue
      if (tokensFound.length > 1) {
        issues.push({
          userId,
          issue: 'MULTIPLE_TOKEN_FIELDS',
          details: tokensFound.map(t => `${t.field}: ${t.tokens.length} token(s)`).join(', '),
          recommendation: 'Migrate to standardized device_tokens array'
        });
      }
      
      // Validate the preferred token
      let preferredToken = null;
      
      // Use token from device_tokens array first if available
      if (tokenFields.device_tokens.length > 0) {
        preferredToken = tokenFields.device_tokens[0];
      } 
      // Then try deviceTokens
      else if (tokenFields.deviceTokens.length > 0) {
        preferredToken = tokenFields.deviceTokens[0];
      }
      // Then try other fields
      else if (tokenFields.fcm_token) {
        preferredToken = tokenFields.fcm_token;
      } else if (tokenFields.fcmToken) {
        preferredToken = tokenFields.fcmToken;
      } else if (tokenFields.fcmTokens.length > 0) {
        preferredToken = tokenFields.fcmTokens[0];
      }
      
      if (preferredToken) {
        try {
          // Check if token is valid by attempting to send a message
          // We won't actually send it - just validate
          await admin.messaging().validateRegistrationToken(preferredToken);
          validTokenCount++;
        } catch (error) {
          issues.push({
            userId,
            issue: 'INVALID_TOKEN',
            details: `Token validation failed: ${error.message}`,
            token: preferredToken.substring(0, 10) + '...',
            recommendation: 'Refresh token on next user login'
          });
        }
      }
      
      // Check if user is missing the standardized device_tokens field
      if (tokenFields.device_tokens.length === 0 && preferredToken) {
        issues.push({
          userId,
          issue: 'MISSING_STANDARDIZED_FIELD',
          details: `Has tokens but missing device_tokens field`,
          recommendation: 'Migrate token to device_tokens array'
        });
      }
    }
  }
  
  // Find any standalone tokens in the device_tokens collection
  const tokensSnapshot = await db.collection('device_tokens').get();
  console.log(`Found ${tokensSnapshot.size} tokens in device_tokens collection`);
  
  // Check for tokens without matching user documents
  for (const tokenDoc of tokensSnapshot.docs) {
    const tokenData = tokenDoc.data();
    const userId = tokenData.userId;
    
    if (userId) {
      // Check if user exists
      const userDoc = await db.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        issues.push({
          tokenId: tokenDoc.id,
          userId,
          issue: 'ORPHANED_TOKEN',
          details: `Token exists but user document doesn't`,
          recommendation: 'Delete orphaned token'
        });
      }
    }
  }
  
  // Print summary
  console.log('\n=== FCM Token Validation Summary ===');
  console.log(`Total users: ${usersSnapshot.size}`);
  console.log(`Users with tokens: ${usersWithTokens} (${Math.round(usersWithTokens/usersSnapshot.size*100)}%)`);
  console.log(`Valid tokens: ${validTokenCount}`);
  console.log(`Issues found: ${issues.length}`);
  
  // Print issues
  if (issues.length > 0) {
    console.log('\n=== Issues ===');
    issues.forEach((issue, index) => {
      console.log(`\nIssue #${index + 1} - ${issue.issue}`);
      console.log(`User ID: ${issue.userId || 'N/A'}`);
      console.log(`Details: ${issue.details}`);
      console.log(`Recommendation: ${issue.recommendation}`);
    });
  }
  
  // Migration suggestions section
  console.log('\n=== Token Usage Statistics ===');
  
  // Count tokens by field
  const fieldStats = {
    'device_tokens': 0,
    'deviceTokens': 0, 
    'fcm_token': 0,
    'fcmToken': 0,
    'fcmTokens': 0
  };
  
  // Re-analyze to gather stats
  for (const userDoc of usersSnapshot.docs) {
    const userData = userDoc.data();
    
    if (Array.isArray(userData.device_tokens) && userData.device_tokens.length > 0) fieldStats.device_tokens++;
    if (Array.isArray(userData.deviceTokens) && userData.deviceTokens.length > 0) fieldStats.deviceTokens++;
    if (userData.fcm_token) fieldStats.fcm_token++;
    if (userData.fcmToken) fieldStats.fcmToken++;
    if (Array.isArray(userData.fcmTokens) && userData.fcmTokens.length > 0) fieldStats.fcmTokens++;
  }
  
  // Print stats
  console.log('Token field usage:');
  Object.entries(fieldStats).forEach(([field, count]) => {
    const percentage = Math.round((count / usersWithTokens) * 100) || 0;
    console.log(`  - ${field}: ${count} users (${percentage}%)`);
  });
  
  console.log('\n=== Development Notes ===');
  console.log('Since we are still in development:');
  console.log('1. No migration will be performed');
  console.log('2. Focus on fixing FCM notification handling in the code');
  console.log('3. Ensure Firebase Cloud Messaging is properly configured');
  console.log('4. Test notifications using the Firebase Console to verify token validity');
  console.log('\nTo test a specific token, use the Firebase Console "Cloud Messaging" section');
}

// Run the validator
validateFcmTokens()
  .then(() => {
    console.log('Validation complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error during validation:', error);
    process.exit(1);
  }); 