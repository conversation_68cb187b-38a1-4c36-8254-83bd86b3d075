import java.util.Properties

val localProperties = Properties().apply {
    val localPropertiesFile = rootProject.file("local.properties")
    if (localPropertiesFile.exists()) {
        load(localPropertiesFile.inputStream())
    }
}

val keyProperties = Properties().apply {
    val keyPropertiesFile = rootProject.file("key.properties")
    if (keyPropertiesFile.exists()) {
        load(keyPropertiesFile.inputStream())
    }
}

val flutterRoot = localProperties.getProperty("flutter.sdk")
    ?: error("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")

val flutterVersionCode = localProperties.getProperty("flutter.versionCode") ?: "1"
val flutterVersionName = localProperties.getProperty("flutter.versionName") ?: "1.0"

plugins {
    id("com.android.application")
    // START: FlutterFire Configuration
    id("com.google.gms.google-services")
    // END: FlutterFire Configuration
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

// Use configuration block to handle version conflicts
configurations.all {
    resolutionStrategy {
        // Force specific versions for conflicting dependencies if needed
        force("org.bouncycastle:bcprov-jdk15on:1.70")
        force("org.bouncycastle:bcpkix-jdk15on:1.70")
    }
}

android {
    namespace = "com.maximummedia.mrtech"
    compileSdk = 35
    ndkVersion = "27.0.12077973"

    // Enable BuildConfig generation
    buildFeatures {
        buildConfig = true
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        // Enable desugaring
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }
    
    // Java compiler options to suppress warnings
    tasks.withType<JavaCompile> {
        options.compilerArgs.add("-Xlint:-options")
    }

    sourceSets {
        getByName("main").java.srcDirs("src/main/kotlin")
    }

    signingConfigs {
        create("release") {
            keyAlias = keyProperties.getProperty("keyAlias")
            keyPassword = keyProperties.getProperty("keyPassword")
            storeFile = file(rootProject.file(keyProperties.getProperty("storeFile")))
            storePassword = keyProperties.getProperty("storePassword")
        }
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.maximummedia.mrtech"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 21
        targetSdk = 34
        versionCode = flutterVersionCode.toInt()
        versionName = flutterVersionName
        multiDexEnabled = true
    }

    buildTypes {
        release {
            // Use our signing config for release builds
            signingConfig = signingConfigs.getByName("release")
            
            // Enable minification for proper mapping file generation
            isMinifyEnabled = true
            isShrinkResources = false
            
            // Keep ProGuard rules for reference
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
                "src/main/proguard-missing-rules.txt"
            )
            
            // Set false to prevent tree-shaking icons
            buildConfigField("Boolean", "TREE_SHAKE_ICONS", "false")
        }
        
        debug {
            isDebuggable = true
            isMinifyEnabled = false
            isShrinkResources = false
        }
    }

    // Add packagingOptions to exclude conflicting classes
    packagingOptions {
        resources {
            excludes += listOf(
                "META-INF/proguard/androidx-annotations.pro",
                "META-INF/LICENSE",
                "META-INF/NOTICE",
                "META-INF/*.kotlin_module",
                "META-INF/AL2.0",
                "META-INF/LGPL2.1"
            )
        }
    }
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.3")
    implementation(kotlin("stdlib-jdk7"))
    
    // Firebase dependencies
    implementation(platform("com.google.firebase:firebase-bom:32.7.2"))
    implementation("com.google.firebase:firebase-analytics")
    
    // Google Play Services
    implementation("com.google.android.gms:play-services-base:18.3.0")
    
    // Play Core dependencies for deferred components (even though disabled in AndroidManifest.xml)
    implementation("com.google.android.play:feature-delivery:2.1.0")
    implementation("com.google.android.play:core-common:2.0.3")
    implementation("com.google.android.gms:play-services-tasks:18.1.0")
    
    // Add multidex support - ensure this is properly imported
    implementation("androidx.multidex:multidex:2.0.1")
    implementation("androidx.appcompat:appcompat:1.6.1")
    
    // Add explicit compatible version of bouncycastle
    implementation("org.bouncycastle:bcprov-jdk15on:1.70")
}

flutter {
    source = "../.."
}
