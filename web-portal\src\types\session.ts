export interface Session {
  uid: string;
  email: string;
  role: string;
  expiresAt: Date;
  refreshToken?: string;
  deviceInfo?: DeviceInfo;
  createdAt: Date;
  lastActivity: Date;
}

export interface DeviceInfo {
  userAgent: string;
  platform: string;
  browser: string;
  ip?: string;
  location?: string;
  deviceId: string;
}

export interface SessionConfig {
  sessionDuration: number; // in milliseconds
  rememberMeDuration: number; // in milliseconds
  inactivityTimeout: number; // in milliseconds
  enableMultiDevice: boolean;
  maxDevices: number;
}

export interface StoredSession {
  token: string;
  refreshToken?: string;
  expiresAt: string;
  rememberMe: boolean;
  deviceId: string;
}

export const DEFAULT_SESSION_CONFIG: SessionConfig = {
  sessionDuration: 2 * 60 * 60 * 1000, // 2 hours
  rememberMeDuration: 30 * 24 * 60 * 60 * 1000, // 30 days
  inactivityTimeout: 30 * 60 * 1000, // 30 minutes
  enableMultiDevice: true,
  maxDevices: 5,
}; 