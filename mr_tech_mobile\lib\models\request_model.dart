import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

enum RequestStatus {
  payment_pending,
  pending,
  approved,
  inProgress,
  in_progress,
  completed,
  cancelled,
  refused,
}

class RequestModel {
  final String id;
  final String customerId;
  final String serviceId;
  final String serviceName;
  final String serviceDescription;
  final String customerIssue;
  final String? anydeskId;
  final String? technicianId;
  final String? technicianName;
  final RequestStatus status;
  final double amount;
  final bool isPaid;
  final bool chatActive;
  final bool sessionActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? scheduledTime;
  final DateTime? sessionStartTime;
  final DateTime? sessionEndTime;
  final int? sessionDuration; // in minutes
  final bool? customerRated;
  final double? rating;
  final String? reviewComment;
  final String? customerFcmToken;

  RequestModel({
    required this.id,
    required this.customerId,
    required this.serviceId,
    required this.serviceName,
    required this.serviceDescription,
    required this.customerIssue,
    this.anydeskId,
    this.technicianId,
    this.technicianName,
    this.status = RequestStatus.pending,
    required this.amount,
    required this.isPaid,
    required this.chatActive,
    this.sessionActive = false,
    required this.createdAt,
    this.updatedAt,
    this.scheduledTime,
    this.sessionStartTime,
    this.sessionEndTime,
    this.sessionDuration,
    this.customerRated,
    this.rating,
    this.reviewComment,
    this.customerFcmToken,
  });

  // Create from Firestore document
  factory RequestModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    try {
      final data = doc.data() ?? {};
      final id = doc.id;
      return RequestModel.fromMap(id, data);
    } catch (e) {
      debugPrint('Error parsing RequestModel from Firestore: $e');
      return RequestModel.empty();
    }
  }

  // Create from document ID and Map
  // PHASE 1: Prefer snake_case but maintain full backward compatibility
  factory RequestModel.fromMap(String id, Map<String, dynamic> data) {
    try {
      // Phase 1 Migration: Prefer snake_case, fallback to camelCase for safety
      // This ensures no crashes during migration process

      // Critical fields with enhanced safety checks
      final bool chatActive =
          data['chat_active'] == true || data['chatActive'] == true;
      final bool isPaid = data['is_paid'] == true || data['isPaid'] == true;
      final bool sessionActive =
          data['session_active'] == true || data['sessionActive'] == true;

      // Log field access patterns for monitoring (can be removed after migration)
      if (data['customer_id'] == null && data['customerId'] != null) {
        debugPrint(
          'RequestModel.fromMap: Using camelCase fallback for customer_id in doc $id',
        );
      }

      // Parse timestamps with enhanced safety
      final createdAtTimestamp = _parseTimestamp(
        data['created_at'] ?? data['createdAt'],
      );
      final updatedAtTimestamp = _parseTimestamp(
        data['updated_at'] ?? data['updatedAt'],
      );

      return RequestModel(
        id: id,
        // Phase 1: Prefer snake_case with safe fallbacks
        customerId: data['customer_id'] ?? data['customerId'] ?? '',
        serviceId: data['service_id'] ?? data['serviceId'] ?? '',
        serviceName:
            data['service_name'] ??
            data['serviceName'] ??
            data['service'] ??
            '',
        serviceDescription:
            data['service_description'] ?? data['serviceDescription'] ?? '',
        customerIssue:
            data['customer_issue'] ??
            data['customerIssue'] ??
            data['description'] ??
            '',
        anydeskId: data['anydesk_id'] ?? data['anydeskId'],
        technicianId: data['technician_id'] ?? data['technicianId'],
        technicianName: data['technician_name'] ?? data['technicianName'],
        status: _parseStatus(data['status']),
        amount: _parseDouble(data['amount'] ?? data['price']),
        isPaid: isPaid,
        chatActive: chatActive,
        sessionActive: sessionActive,
        createdAt: createdAtTimestamp ?? DateTime.now(), // Safe default
        updatedAt: updatedAtTimestamp,
        scheduledTime: _parseTimestamp(
          data['scheduled_time'] ?? data['scheduledTime'],
        ),
        sessionStartTime: _parseTimestamp(
          data['session_start_time'] ?? data['sessionStartTime'],
        ),
        sessionEndTime: _parseTimestamp(
          data['session_end_time'] ?? data['sessionEndTime'],
        ),
        sessionDuration:
            data['session_duration'] ?? data['sessionDuration'] ?? 0,
        customerRated: data['customer_rated'] ?? data['customerRated'],
        rating: (data['rating'] ?? data['rating'])?.toDouble(),
        reviewComment: data['review_comment'] ?? data['reviewComment'],
        customerFcmToken:
            data['customer_fcm_token'] ?? data['customerFcmToken'],
      );
    } catch (e) {
      debugPrint('Error parsing RequestModel from map: $e for ID: $id');
      debugPrint('Data: $data');
      // Return safe empty model instead of crashing
      return RequestModel.empty();
    }
  }

  // Convert to Map for Firestore
  // PHASE 2: Write ONLY snake_case (no more dual-write)
  // This ensures no data loss during migration
  Map<String, dynamic> toFirestore() {
    final Map<String, dynamic> data = {
      // Use ONLY snake_case fields - standardized format
      'customer_id': customerId,
      'service_id': serviceId,
      'service_name': serviceName,
      'service_description': serviceDescription,
      'customer_issue': customerIssue,
      'anydesk_id': anydeskId,
      'technician_id': technicianId,
      'technician_name': technicianName,
      'status': status.toString().split('.').last,
      'amount': amount,
      'is_paid': isPaid,
      'chat_active': chatActive,
      'session_active': sessionActive,
      'created_at': createdAt,
      'updated_at': updatedAt ?? FieldValue.serverTimestamp(),
      'scheduled_time': scheduledTime,
      'session_start_time': sessionStartTime,
      'session_end_time': sessionEndTime,
      'session_duration': sessionDuration,
      'customer_rated': customerRated,
      'rating': rating,
      'review_comment': reviewComment,
      'customer_fcm_token': customerFcmToken,
      'is_visible': true,
    };

    return data;
  }

  // Create copy with updated fields
  RequestModel copyWith({
    String? id,
    String? customerId,
    String? serviceId,
    String? serviceName,
    String? serviceDescription,
    String? customerIssue,
    String? anydeskId,
    String? technicianId,
    String? technicianName,
    RequestStatus? status,
    double? amount,
    bool? isPaid,
    bool? chatActive,
    bool? sessionActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? scheduledTime,
    DateTime? sessionStartTime,
    DateTime? sessionEndTime,
    int? sessionDuration,
    bool? customerRated,
    double? rating,
    String? reviewComment,
    String? customerFcmToken,
  }) {
    return RequestModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      serviceId: serviceId ?? this.serviceId,
      serviceName: serviceName ?? this.serviceName,
      serviceDescription: serviceDescription ?? this.serviceDescription,
      customerIssue: customerIssue ?? this.customerIssue,
      anydeskId: anydeskId ?? this.anydeskId,
      technicianId: technicianId ?? this.technicianId,
      technicianName: technicianName ?? this.technicianName,
      status: status ?? this.status,
      amount: amount ?? this.amount,
      isPaid: isPaid ?? this.isPaid,
      chatActive: chatActive ?? this.chatActive,
      sessionActive: sessionActive ?? this.sessionActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      sessionStartTime: sessionStartTime ?? this.sessionStartTime,
      sessionEndTime: sessionEndTime ?? this.sessionEndTime,
      sessionDuration: sessionDuration ?? this.sessionDuration,
      customerRated: customerRated ?? this.customerRated,
      rating: rating ?? this.rating,
      reviewComment: reviewComment ?? this.reviewComment,
      customerFcmToken: customerFcmToken ?? this.customerFcmToken,
    );
  }

  // Helper to parse status string to enum
  static RequestStatus _parseStatus(String? status) {
    switch (status) {
      case 'payment_pending':
        return RequestStatus.payment_pending;
      case 'approved':
        return RequestStatus.approved;
      case 'inProgress':
      case 'in-progress':
      case 'in_progress':
        return RequestStatus.inProgress;
      case 'completed':
        return RequestStatus.completed;
      case 'cancelled':
        return RequestStatus.cancelled;
      case 'refused':
        return RequestStatus.refused;
      default:
        return RequestStatus.pending; // Default to pending
    }
  }

  // Helper to parse double
  static double _parseDouble(dynamic value) {
    if (value is num) {
      return value.toDouble();
    } else if (value is String) {
      return double.parse(value);
    } else {
      throw Exception('Invalid format for amount');
    }
  }

  // Helper to parse timestamp
  static DateTime? _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) {
      return null;
    } else if (timestamp is Timestamp) {
      return timestamp.toDate();
    } else if (timestamp is DateTime) {
      return timestamp;
    } else if (timestamp is int) {
      // Handle milliseconds since epoch
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        debugPrint('Error parsing timestamp string: $e');
        return null;
      }
    } else {
      debugPrint('Unknown timestamp format: ${timestamp.runtimeType}');
      return null;
    }
  }

  // Factory method to create an empty RequestModel
  static RequestModel empty() {
    return RequestModel(
      id: '',
      customerId: '',
      serviceId: '',
      serviceName: '',
      serviceDescription: '',
      customerIssue: '',
      anydeskId: null,
      technicianId: null,
      technicianName: null,
      status: RequestStatus.pending,
      amount: 0.0,
      isPaid: false,
      chatActive: false,
      sessionActive: false,
      createdAt: DateTime.now(),
      updatedAt: null,
      scheduledTime: null,
      sessionStartTime: null,
      sessionEndTime: null,
      sessionDuration: null,
      customerRated: null,
      rating: null,
      reviewComment: null,
      customerFcmToken: null,
    );
  }
}
