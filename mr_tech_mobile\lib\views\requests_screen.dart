import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/database_service.dart';
import '../services/request_service.dart';
import '../models/request_model.dart';
import 'package:intl/intl.dart';
import '../widgets/text_styles.dart';
import 'main_navigation.dart';

class RequestsScreen extends StatefulWidget {
  const RequestsScreen({super.key});

  @override
  State<RequestsScreen> createState() => _RequestsScreenState();
}

class _RequestsScreenState extends State<RequestsScreen> {
  bool _isLoading = true;
  List<RequestModel> _requests = [];
  String? _errorMessage;
  String? _selectedFilter;

  // List of possible status filters
  final List<String> _statusFilters = [
    'all',
    'pending',
    'in_progress',
    'completed',
    'cancelled',
  ];

  final RequestService _requestService = RequestService();

  @override
  void initState() {
    super.initState();
    _loadRequests();
  }

  Future<void> _loadRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get database service
      final databaseService = Provider.of<DatabaseService>(
        context,
        listen: false,
      );

      // Load user requests
      _requests = await databaseService.getUserRequests();

      // Sort by date (newest first)
      _requests.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // Setup listeners for active requests
      _setupRequestListeners();
    } catch (e) {
      debugPrint('Error loading requests: $e');
      setState(() {
        _errorMessage = 'Failed to load requests. Please try again.';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Set up listeners for active requests to ensure notifications for status changes
  void _setupRequestListeners() {
    // Get active requests (pending, approved, in_progress)
    final activeRequests =
        _requests
            .where(
              (request) =>
                  request.status == RequestStatus.pending ||
                  request.status == RequestStatus.approved ||
                  request.status == RequestStatus.inProgress,
            )
            .toList();

    if (activeRequests.isNotEmpty) {
      debugPrint(
        'Setting up listeners for ${activeRequests.length} active requests',
      );

      // Set up listener for each active request
      for (final request in activeRequests) {
        debugPrint('Setting up listener for request: ${request.id}');
        final requestService = RequestService();
        requestService.setupRequestStatusListener(request.id);
      }
    }
  }

  Color _getStatusColor(RequestStatus status) {
    switch (status) {
      case RequestStatus.payment_pending:
        return Colors.grey;
      case RequestStatus.pending:
        return Colors.amber;
      case RequestStatus.approved:
        return Colors.blue;
      case RequestStatus.inProgress:
      case RequestStatus.in_progress:
        return Colors.indigo;
      case RequestStatus.completed:
        return Colors.green;
      case RequestStatus.cancelled:
        return Colors.red;
      case RequestStatus.refused:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(RequestStatus status) {
    switch (status) {
      case RequestStatus.payment_pending:
        return Icons.payments_outlined;
      case RequestStatus.pending:
        return Icons.hourglass_empty_rounded;
      case RequestStatus.approved:
        return Icons.check_circle_outline_rounded;
      case RequestStatus.inProgress:
      case RequestStatus.in_progress:
        return Icons.engineering_rounded;
      case RequestStatus.completed:
        return Icons.task_alt_rounded;
      case RequestStatus.cancelled:
        return Icons.cancel_outlined;
      case RequestStatus.refused:
        return Icons.block_outlined;
    }
  }

  // Show request details dialog
  void _viewRequestDetails(BuildContext context, RequestModel request) {
    final colorScheme = Theme.of(context).colorScheme;
    final isRtl = Provider.of<TranslationService>(context, listen: false).isRtl;
    final statusColor = _getStatusColor(request.status);

    // Update FCM token when viewing the request
    _requestService.updateFCMTokenWhenViewing(request.id);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: colorScheme.surface,
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getStatusIcon(request.status),
                    color: statusColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  _translate('Request Details'),
                  style: AppTextStyles.headingSmall(context),
                ),
              ],
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: Colors.grey.shade300),
            ),
            contentPadding: const EdgeInsets.all(20),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _detailRow(
                    Icons.tag_rounded,
                    _translate('Request ID'),
                    '#REQ-${request.id.substring(0, 4).toUpperCase()}',
                    isRtl,
                  ),
                  const SizedBox(height: 16),
                  _detailRow(
                    Icons.miscellaneous_services_rounded,
                    _translate('Service'),
                    _translate(request.serviceName),
                    isRtl,
                  ),
                  const SizedBox(height: 16),
                  _detailRow(
                    _getStatusIcon(request.status),
                    _translate('Status'),
                    _getTranslatedStatus(request.status),
                    isRtl,
                    valueColor: _getStatusColor(request.status),
                  ),
                  const SizedBox(height: 16),
                  _detailRow(
                    Icons.calendar_today_rounded,
                    _translate('Date'),
                    DateFormat('yyyy-MM-dd').format(request.createdAt),
                    isRtl,
                  ),
                  if (request.isPaid) ...[
                    const SizedBox(height: 16),
                    _detailRow(
                      Icons.payment_rounded,
                      _translate('Payment'),
                      _translate('Paid'),
                      isRtl,
                      valueColor: Colors.green,
                    ),
                  ],
                  const SizedBox(height: 20),
                  Divider(height: 1, color: Colors.grey.shade300),
                  const SizedBox(height: 16),
                  Text(
                    _translate('Description'),
                    style: AppTextStyles.labelMedium(
                      context,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    request.customerIssue,
                    style: AppTextStyles.bodyMedium(
                      context,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  if (request.technicianName != null) ...[
                    const SizedBox(height: 20),
                    Divider(height: 1, color: Colors.grey.shade300),
                    const SizedBox(height: 16),
                    _detailRow(
                      Icons.person_rounded,
                      _translate('Technician'),
                      request.technicianName!,
                      isRtl,
                    ),
                  ],
                  if (request.anydeskId != null &&
                      request.anydeskId!.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    _detailRow(
                      Icons.desktop_windows_rounded,
                      _translate('AnyDesk ID'),
                      request.anydeskId!,
                      isRtl,
                      copyable: true,
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              OutlinedButton(
                onPressed: () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey.shade300),
                ),
                child: Text(_translate('Close')),
              ),
              if (request.chatActive)
                FilledButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    // TODO: Navigate to chat screen
                  },
                  icon: const Icon(Icons.chat_rounded, size: 18),
                  label: Text(_translate('Open Chat')),
                  style: FilledButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: Colors.white,
                    elevation: 0,
                  ),
                ),
            ],
          ),
    );
  }

  Widget _detailRow(
    IconData icon,
    String label,
    String value,
    bool isRtl, {
    Color? valueColor,
    bool copyable = false,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Row(
      children: [
        Icon(icon, size: 18, color: colorScheme.primary),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall(
                  context,
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      value,
                      style: AppTextStyles.bodyMedium(
                        context,
                        color: valueColor ?? colorScheme.onSurface,
                      ),
                    ),
                  ),
                  if (copyable)
                    IconButton(
                      icon: Icon(
                        Icons.copy,
                        size: 16,
                        color: colorScheme.primary,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 36,
                        minHeight: 36,
                      ),
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        // TODO: Implement copy to clipboard
                      },
                    ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to get translations easily
  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(key);
  }

  void _navigateToServicesScreen() {
    // Navigate to the main navigation with the services tab selected
    // This preserves the app state while correctly showing the bottom navigation
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder:
            (context) => const MainNavigation(
              initialIndex: 2, // Services tab index
            ),
      ),
    );
  }

  // Filter requests based on selected filter
  List<RequestModel> get _filteredRequests {
    if (_selectedFilter == null || _selectedFilter == 'all') {
      return _requests;
    }

    // Convert filter to RequestStatus
    switch (_selectedFilter) {
      case 'pending':
        return _requests
            .where(
              (r) =>
                  r.status == RequestStatus.pending ||
                  r.status == RequestStatus.payment_pending ||
                  r.status == RequestStatus.approved,
            )
            .toList();
      case 'in_progress':
        return _requests
            .where((r) => r.status == RequestStatus.inProgress)
            .toList();
      case 'completed':
        return _requests
            .where((r) => r.status == RequestStatus.completed)
            .toList();
      case 'cancelled':
        return _requests
            .where(
              (r) =>
                  r.status == RequestStatus.cancelled ||
                  r.status == RequestStatus.refused,
            )
            .toList();
      default:
        return _requests;
    }
  }

  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final isRtl = translationService.isRtl;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Floating header
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            leading: BackButton(color: colorScheme.onSurface),
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 56), // Space for back button
                      Expanded(
                        child: Text(
                          _translate('Requests'),
                          style: AppTextStyles.headingMedium(
                            context,
                            color: colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 56), // Balance the layout
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Main content
          _isLoading
              ? const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
              : _errorMessage != null
              ? SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.error_outline_rounded,
                          size: 48,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _translate('Error'),
                        style: AppTextStyles.headingSmall(
                          context,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Text(
                          _translate(_errorMessage!),
                          style: AppTextStyles.bodyMedium(
                            context,
                            color: colorScheme.onSurfaceVariant,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 24),
                      FilledButton.icon(
                        onPressed: _loadRequests,
                        icon: const Icon(Icons.refresh_rounded),
                        label: Text(_translate('Try Again')),
                        style: FilledButton.styleFrom(
                          backgroundColor: colorScheme.primary,
                          foregroundColor: Colors.white,
                          elevation: 0,
                        ),
                      ),
                      const SizedBox(height: 16),
                      OutlinedButton.icon(
                        onPressed: _navigateToServicesScreen,
                        icon: const Icon(Icons.add_rounded),
                        label: Text(_translate('Create New Request')),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                    ],
                  ),
                ),
              )
              : SliverToBoxAdapter(
                child: Column(
                  children: [
                    // Status filter tabs with light borders
                    if (_requests.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: SizedBox(
                          height: 50,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            padding: EdgeInsets.zero,
                            itemCount: _statusFilters.length,
                            itemBuilder: (context, index) {
                              final filter = _statusFilters[index];
                              final isSelected = _selectedFilter == filter;

                              // Get color and icon based on filter
                              Color filterColor;
                              IconData filterIcon;

                              switch (filter) {
                                case 'pending':
                                  filterColor = Colors.amber;
                                  filterIcon = Icons.hourglass_empty_rounded;
                                  break;
                                case 'in_progress':
                                  filterColor = Colors.indigo;
                                  filterIcon = Icons.engineering_rounded;
                                  break;
                                case 'completed':
                                  filterColor = Colors.green;
                                  filterIcon = Icons.task_alt_rounded;
                                  break;
                                case 'cancelled':
                                  filterColor = Colors.red;
                                  filterIcon = Icons.cancel_outlined;
                                  break;
                                default:
                                  filterColor = colorScheme.primary;
                                  filterIcon = Icons.all_inbox_rounded;
                              }

                              // Get translated filter name
                              String translatedFilter;
                              switch (filter) {
                                case 'all':
                                  translatedFilter = _translate('All');
                                  break;
                                case 'pending':
                                  translatedFilter = _translate('Pending');
                                  break;
                                case 'in_progress':
                                  translatedFilter = _translate('In Progress');
                                  break;
                                case 'completed':
                                  translatedFilter = _translate('Completed');
                                  break;
                                case 'cancelled':
                                  translatedFilter = _translate('Cancelled');
                                  break;
                                default:
                                  translatedFilter = _translate(filter);
                              }

                              return Padding(
                                padding: const EdgeInsets.only(right: 8),
                                child: FilterChip(
                                  selected: isSelected,
                                  label: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        filterIcon,
                                        size: 16,
                                        color:
                                            isSelected
                                                ? Colors.white
                                                : colorScheme.onSurface,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        translatedFilter,
                                        style: AppTextStyles.bodyMedium(
                                          context,
                                          color:
                                              isSelected
                                                  ? Colors.white
                                                  : colorScheme.onSurface,
                                        ),
                                      ),
                                    ],
                                  ),
                                  backgroundColor: colorScheme.surface,
                                  selectedColor: filterColor,
                                  side: BorderSide(color: Colors.grey.shade300),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  onSelected: (selected) {
                                    setState(() {
                                      _selectedFilter =
                                          selected ? filter : null;
                                    });
                                  },
                                ),
                              );
                            },
                          ),
                        ),
                      ),

                    // Requests list or empty state
                    _filteredRequests.isEmpty
                        ? SizedBox(
                          height: MediaQuery.of(context).size.height * 0.6,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: colorScheme.primary.withOpacity(0.1),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    _selectedFilter != null
                                        ? Icons.filter_alt_off
                                        : Icons.assignment_outlined,
                                    size: 48,
                                    color: colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _selectedFilter != null
                                      ? _translate(
                                        'No requests match the filter',
                                      )
                                      : _translate('No requests yet'),
                                  style: AppTextStyles.headingSmall(
                                    context,
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _selectedFilter != null
                                      ? _translate(
                                        'Try changing the filter or create a new request',
                                      )
                                      : _translate(
                                        'Create your first service request to get started',
                                      ),
                                  style: AppTextStyles.bodyMedium(
                                    context,
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                if (_selectedFilter != null)
                                  OutlinedButton.icon(
                                    onPressed: () {
                                      setState(() {
                                        _selectedFilter = null;
                                      });
                                    },
                                    icon: const Icon(Icons.filter_alt_off),
                                    label: Text(_translate('Clear filters')),
                                    style: OutlinedButton.styleFrom(
                                      side: BorderSide(
                                        color: Colors.grey.shade300,
                                      ),
                                    ),
                                  ),
                                if (_selectedFilter == null) ...[
                                  FilledButton.icon(
                                    onPressed: _navigateToServicesScreen,
                                    icon: const Icon(Icons.add_rounded),
                                    label: Text(
                                      _translate('Create New Request'),
                                    ),
                                    style: FilledButton.styleFrom(
                                      backgroundColor: colorScheme.primary,
                                      foregroundColor: Colors.white,
                                      elevation: 0,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        )
                        : RefreshIndicator(
                          onRefresh: _loadRequests,
                          child: ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            padding: const EdgeInsets.all(16),
                            itemCount: _filteredRequests.length,
                            separatorBuilder:
                                (context, index) => const SizedBox(height: 12),
                            itemBuilder: (context, index) {
                              final request = _filteredRequests[index];
                              return _buildRequestCard(request, isRtl);
                            },
                          ),
                        ),
                  ],
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildRequestCard(RequestModel request, bool isRtl) {
    final colorScheme = Theme.of(context).colorScheme;
    final statusColor = _getStatusColor(request.status);

    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color:
              request.status == RequestStatus.inProgress
                  ? statusColor.withOpacity(0.5)
                  : Colors.grey.shade300,
          width: request.status == RequestStatus.inProgress ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          // Update FCM token when viewing details - IMPORTANT for notifications
          _requestService.updateFCMTokenWhenViewing(request.id);
          _viewRequestDetails(context, request);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top section with ID and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '#REQ-${request.id.substring(0, 4).toUpperCase()}',
                    style: AppTextStyles.bodySmall(
                      context,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: statusColor.withOpacity(0.3)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getStatusIcon(request.status),
                          size: 12,
                          color: statusColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getTranslatedStatus(request.status),
                          style: AppTextStyles.bodySmall(
                            context,
                            color: statusColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Service title
              Text(
                _translate(request.serviceName),
                style: AppTextStyles.headingSmall(
                  context,
                  color: colorScheme.onSurface,
                ),
              ),

              const SizedBox(height: 8),

              // Description
              Text(
                request.customerIssue,
                style: AppTextStyles.bodyMedium(
                  context,
                  color: colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 16),

              // Footer section with date and technician
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 16,
                        color: colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        _getFormattedTime(request.createdAt),
                        style: AppTextStyles.bodySmall(
                          context,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                  if (request.technicianName != null)
                    Row(
                      children: [
                        Icon(
                          Icons.person_outline,
                          size: 16,
                          color: statusColor,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          request.technicianName!,
                          style: AppTextStyles.bodySmall(
                            context,
                            color: statusColor,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getFormattedTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}${_translate('d ago')}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}${_translate('h ago')}';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}${_translate('m ago')}';
    } else {
      return _translate('Just now');
    }
  }

  // Helper method to get translated status
  String _getTranslatedStatus(RequestStatus status) {
    switch (status) {
      case RequestStatus.payment_pending:
        return _translate('Payment Pending');
      case RequestStatus.pending:
        return _translate('Pending');
      case RequestStatus.approved:
        return _translate('Approved');
      case RequestStatus.inProgress:
      case RequestStatus.in_progress:
        return _translate('In Progress');
      case RequestStatus.completed:
        return _translate('Completed');
      case RequestStatus.cancelled:
        return _translate('Cancelled');
      case RequestStatus.refused:
        return _translate('Refused');
    }
  }
}
