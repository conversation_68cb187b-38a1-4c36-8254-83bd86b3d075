import { Timestamp } from 'firebase/firestore';

export interface ServiceModel {
  id: string;
  name: string | Record<string, string>; // Can be a string or translations map
  description: string | Record<string, string>; // Can be a string or translations map
  category: string;
  base_price: number;
  image_url: string;
  is_active: boolean;
  metadata?: Record<string, any>;
  estimated_duration: number; // in minutes
  created_at: Timestamp | Date;
  updated_at?: Timestamp | Date;
}

export interface CreateServiceInput {
  name: string | Record<string, string>;
  description: string | Record<string, string>;
  category: string;
  base_price: number;
  image_url: string;
  is_active?: boolean;
  metadata?: Record<string, any>;
  estimated_duration: number;
}

export interface UpdateServiceInput {
  name?: string | Record<string, string>;
  description?: string | Record<string, string>;
  category?: string;
  base_price?: number;
  image_url?: string;
  is_active?: boolean;
  metadata?: Record<string, any>;
  estimated_duration?: number;
} 