// Load environment variables
require('dotenv').config();

const { initializeApp } = require('firebase/app');
const { getAuth, createUserWithEmailAndPassword, signOut } = require('firebase/auth');
const { getFirestore, doc, setDoc, serverTimestamp } = require('firebase/firestore');

// Get command line arguments
const args = process.argv.slice(2);
const email = args[0];
const password = args[1];
const displayName = args[2] || 'Admin User';

if (!email || !password) {
  console.error('Usage: node create-admin.js <email> <password> [displayName]');
  process.exit(1);
}

// Firebase configuration - directly from .env file
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
  databaseURL: process.env.REACT_APP_FIREBASE_DATABASE_URL
};

console.log('Firebase Config:', firebaseConfig);

// Create an admin user
async function createAdminUser() {
  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    const db = getFirestore(app);
    
    console.log(`Creating admin user: ${email}`);
    
    // Create the user with Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    console.log(`User created with UID: ${user.uid}`);
    
    // Create a document in the users collection
    await setDoc(doc(db, 'users', user.uid), {
      email,
      displayName,
      role: 'admin',  // Set role as admin
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      active: true
    });
    
    console.log(`Admin user document created in Firestore`);
    
    // Sign out the user
    await signOut(auth);
    console.log(`Done! Admin user created successfully. You can now login with email: ${email}`);
    
    return user.uid;
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
}

// Run the function
createAdminUser()
  .then(() => {
    console.log('Admin user creation completed.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Admin user creation failed:', error);
    process.exit(1);
  }); 