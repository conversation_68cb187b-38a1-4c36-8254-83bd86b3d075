import 'package:flutter/material.dart';
import '../models/request_model.dart';
import '../services/payment_service.dart';
import '../services/request_service.dart';
import '../services/translation_service.dart';
import '../utils/payment_utils.dart';
import 'package:provider/provider.dart';
import 'payment_success_screen.dart';
import '../widgets/text_styles.dart';

class PaymentScreen extends StatefulWidget {
  final RequestModel request;
  final Map<String, dynamic>? serviceData;

  const PaymentScreen({super.key, required this.request, this.serviceData});

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  bool _isLoading = false;
  final PaymentService _paymentService = PaymentService();
  final RequestService _requestService = RequestService();

  // User payment info
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  // Initialize Paymob integration
  Future<void> _initializePaymobSDK() async {
    try {
      await _paymentService.initialize();
    } catch (e) {
      debugPrint('Error initializing payment integration: $e');
    }
  }

  // Initialize form with user data
  Future<void> _initializeForm() async {
    try {
      final userInfo = await PaymentUtils.getUserBillingInfo();

      _nameController.text = userInfo['full_name'] ?? '';
      _emailController.text = userInfo['email'] ?? '';
      _phoneController.text = userInfo['phone'] ?? '';
    } catch (e) {
      debugPrint('Error initializing form: $e');
    }
  }

  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Check if this is a temp request that needs to be created after payment
      final bool isTemporaryRequest =
          widget.serviceData != null && widget.request.id == 'temp_id';

      // If not a temporary request, verify it exists before proceeding
      if (!isTemporaryRequest) {
        final requestExists = await _requestService.checkRequestExists(
          widget.request.id,
        );
        if (!requestExists) {
          if (mounted) {
            PaymentUtils.showPaymentErrorDialog(
              context,
              _translate(
                'This service request no longer exists. Please create a new request.',
              ),
            );
            setState(() => _isLoading = false);
          }
          return;
        }
      }

      // Prepare billing data for payment
      final billingData = PaymentUtils.formatBillingData(
        fullName: _nameController.text,
        email: _emailController.text,
        phone: _phoneController.text,
        address: 'Remote Service', // No physical address needed
        city: 'N/A',
        country: 'Egypt', // Default country for billing
      );

      // Use the payment implementation
      final success = await _paymentService.processPayment(
        context: context,
        request: widget.request,
        billingData: billingData,
        serviceData: widget.serviceData, // Pass service data if available
      );

      if (success && mounted) {
        setState(() => _isLoading = false);

        // Payment was successful, navigate to success screen
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder:
                  (context) => PaymentSuccessScreen(request: widget.request),
            ),
          );
        }
      } else if (mounted) {
        setState(() => _isLoading = false);
        PaymentUtils.showPaymentErrorDialog(
          context,
          _translate('Payment was not completed'),
        );
      }
    } catch (e) {
      debugPrint('Error during payment process: $e');
      if (mounted) {
        String errorMsg = e.toString();

        // Simplify error message for user display
        if (errorMsg.contains('Request not found') ||
            errorMsg.contains('Request no longer exists')) {
          errorMsg = _translate(
            'This service request is no longer valid. Please create a new request.',
          );
        } else if (errorMsg.contains('not-found')) {
          errorMsg = _translate(
            'Unable to process payment. Please create a new service request.',
          );
        } else {
          errorMsg =
              '${_translate('Payment error')}: ${errorMsg.replaceAll('Exception: ', '')}';
        }

        setState(() => _isLoading = false);
        PaymentUtils.showPaymentErrorDialog(context, errorMsg);

        // If the request doesn't exist, go back to home after error
        if (errorMsg.contains('request is no longer valid')) {
          // Wait a moment before navigating back
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              Navigator.of(context).popUntil((route) => route.isFirst);
            }
          });
        }
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(key);
  }

  Widget _buildPaymentDetailCard() {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _translate('Payment Details'),
              style: AppTextStyles.headingSmall(
                context,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Divider(color: Colors.grey.shade300),
            const SizedBox(height: 12),

            _buildDetailRow(
              Icons.miscellaneous_services_outlined,
              _translate('Service'),
              _translate(widget.request.serviceName),
            ),
            const SizedBox(height: 12),

            _buildDetailRow(
              Icons.description_outlined,
              _translate('Description'),
              widget.request.customerIssue.length > 60
                  ? '${widget.request.customerIssue.substring(0, 60)}...'
                  : widget.request.customerIssue,
            ),
            const SizedBox(height: 16),

            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.price_check_rounded,
                    color: colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _translate('Total Amount'),
                      style: AppTextStyles.headingSmall(
                        context,
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ),
                  Text(
                    '${_translate('L.E')} ${widget.request.amount.toStringAsFixed(0)}',
                    style: AppTextStyles.headingSmall(
                      context,
                      color: colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    final colorScheme = Theme.of(context).colorScheme;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 18, color: colorScheme.onSurfaceVariant),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall(
                  context,
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: AppTextStyles.bodyMedium(
                  context,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    bool isRequired = true,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: (value) {
        if (isRequired && (value == null || value.trim().isEmpty)) {
          return translationService.translate('This field is required');
        }
        if (label == 'Email' && value != null && !value.contains('@')) {
          return translationService.translate('Please enter a valid email');
        }
        return null;
      },
      style: AppTextStyles.bodyMedium(context),
      decoration: InputDecoration(
        prefixIcon: Icon(icon, color: colorScheme.primary),
        labelText: translationService.translate(label),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        filled: true,
        fillColor: colorScheme.surface,
      ),
    );
  }

  Widget _buildPaymentMethodCard() {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _translate('Payment Method'),
              style: AppTextStyles.headingSmall(
                context,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),

            // Credit/Debit Card option
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.primary.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.credit_card,
                      color: colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _translate('Credit/Debit Card'),
                          style: AppTextStyles.buttonMedium(
                            context,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _translate('Secure payment via Paymob'),
                          style: AppTextStyles.bodySmall(
                            context,
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.check_circle,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityInfoCard() {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.security,
                    color: Colors.green.shade700,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  _translate('Secure Payment'),
                  style: AppTextStyles.headingSmall(
                    context,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildSecurityFeature(
              Icons.lock_outline,
              _translate('256-bit SSL encryption'),
            ),
            _buildSecurityFeature(
              Icons.verified_user_outlined,
              _translate('PCI DSS compliant'),
            ),
            _buildSecurityFeature(
              Icons.shield_outlined,
              _translate('Your data is protected'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityFeature(IconData icon, String text) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.green.shade700),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: AppTextStyles.bodySmall(
                context,
                color: colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            leading: BackButton(color: colorScheme.onSurface),
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 56), // Space for back button
                      Expanded(
                        child: Text(
                          _translate('Complete Payment'),
                          style: AppTextStyles.headingMedium(
                            context,
                            color: colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 56), // Balance the layout
                    ],
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  _buildPaymentDetailCard(),
                  const SizedBox(height: 16),
                  _buildPaymentMethodCard(),
                  const SizedBox(height: 16),
                  _buildSecurityInfoCard(),
                  const SizedBox(height: 24),
                  Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _translate('Billing Information'),
                          style: AppTextStyles.headingSmall(context),
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _nameController,
                          label: 'Full Name',
                          icon: Icons.person_outline,
                        ),
                        const SizedBox(height: 12),
                        _buildTextField(
                          controller: _emailController,
                          label: 'Email',
                          icon: Icons.email_outlined,
                          keyboardType: TextInputType.emailAddress,
                        ),
                        const SizedBox(height: 12),
                        _buildTextField(
                          controller: _phoneController,
                          label: 'Phone Number',
                          icon: Icons.phone_outlined,
                          keyboardType: TextInputType.phone,
                        ),
                        const SizedBox(height: 16),

                        // Service Information Note
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: Colors.blue.shade700,
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  _translate(
                                    'This is a remote technical support service. No physical delivery required.',
                                  ),
                                  style: AppTextStyles.bodySmall(
                                    context,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 100), // Space for bottom button
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: FilledButton.icon(
            onPressed: _isLoading ? null : _processPayment,
            icon:
                _isLoading
                    ? Container()
                    : const Icon(Icons.credit_card, size: 20),
            label:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Text(
                      _translate('Pay Now'),
                      style: AppTextStyles.buttonMedium(
                        context,
                        color: Colors.white,
                      ),
                    ),
            style: FilledButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: colorScheme.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
