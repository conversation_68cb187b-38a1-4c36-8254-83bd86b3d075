import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  onAuthStateChanged,
  type User as FirebaseUser,
} from 'firebase/auth';
import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { AUTH_ERROR_CODES, type User, type LoginCredentials, type SignupData, type UserRole } from '../types/auth';
import sessionService from './sessionService';

class AuthService {
  // Convert Firebase user to our User type
  private async convertFirebaseUser(firebaseUser: FirebaseUser): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      
      if (!userDoc.exists()) {
        console.error('User document not found for:', firebaseUser.uid);
        return null;
      }

      const userData = userDoc.data();
      
      return {
        uid: firebaseUser.uid,
        email: firebaseUser.email || '',
        name: userData.name || userData.display_name || firebaseUser.displayName || '',
        role: userData.role || 'customer',
        phone: userData.phone,
        photo_url: userData.photo_url || firebaseUser.photoURL,
        created_at: userData.created_at?.toDate(),
        updated_at: userData.updated_at?.toDate(),
        is_active: userData.is_active !== false,
        last_login: userData.last_login?.toDate(),
        fcm_token: userData.fcm_token,
        device_type: 'web',
      };
    } catch (error) {
      console.error('Error converting Firebase user:', error);
      return null;
    }
  }

  // Login with email and password
  async login({ email, password, rememberMe = false }: LoginCredentials & { rememberMe?: boolean }): Promise<User> {
    try {
      // Set persistence before login
      await sessionService.setPersistence(rememberMe);
      
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      
      // Update last login
      await updateDoc(doc(db, 'users', userCredential.user.uid), {
        last_login: serverTimestamp(),
        device_type: 'web',
      });

      const user = await this.convertFirebaseUser(userCredential.user);
      
      if (!user) {
        throw new Error('Failed to load user data');
      }

      // Check if user is active
      if (!user.is_active) {
        await signOut(auth);
        throw new Error('Your account has been deactivated. Please contact support.');
      }

      // Check if user has appropriate role for web portal
      if (user.role === 'customer') {
        await signOut(auth);
        throw new Error('This portal is for technicians and administrators only.');
      }

      // Create session after successful login
      await sessionService.createSession(userCredential.user, rememberMe);

      return user;
    } catch (error: any) {
      const errorMessage = AUTH_ERROR_CODES[error.code as keyof typeof AUTH_ERROR_CODES] || error.message;
      throw new Error(errorMessage);
    }
  }

  // Create new user account (admin only)
  async signup(data: SignupData): Promise<User> {
    try {
      // Only admins can create new accounts
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('You must be logged in as an admin to create accounts');
      }

      const currentUserData = await this.getCurrentUser();
      if (!currentUserData || currentUserData.role !== 'admin') {
        throw new Error('Only administrators can create new accounts');
      }

      // Create the user account
      const userCredential = await createUserWithEmailAndPassword(auth, data.email, data.password);
      
      // Update display name
      await updateProfile(userCredential.user, {
        displayName: data.name,
      });

      // Create user document in Firestore
      await setDoc(doc(db, 'users', userCredential.user.uid), {
        email: data.email,
        name: data.name,
        role: data.role || 'technician',
        phone: data.phone || '',
        created_at: serverTimestamp(),
        updated_at: serverTimestamp(),
        is_active: true,
        device_type: 'web',
      });

      const user = await this.convertFirebaseUser(userCredential.user);
      
      if (!user) {
        throw new Error('Failed to create user account');
      }

      return user;
    } catch (error: any) {
      const errorMessage = AUTH_ERROR_CODES[error.code as keyof typeof AUTH_ERROR_CODES] || error.message;
      throw new Error(errorMessage);
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      await sessionService.endSession('logout');
    } catch (error) {
      console.error('Logout error:', error);
      throw new Error('Failed to logout');
    }
  }

  // Logout from all devices
  async logoutAllDevices(): Promise<void> {
    try {
      const user = auth.currentUser;
      if (user) {
        await sessionService.endAllSessions(user.uid);
      }
    } catch (error) {
      console.error('Logout all devices error:', error);
      throw new Error('Failed to logout from all devices');
    }
  }

  // Send password reset email
  async resetPassword(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      const errorMessage = AUTH_ERROR_CODES[error.code as keyof typeof AUTH_ERROR_CODES] || error.message;
      throw new Error(errorMessage);
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    const firebaseUser = auth.currentUser;
    
    if (!firebaseUser) {
      return null;
    }

    return this.convertFirebaseUser(firebaseUser);
  }

  // Subscribe to auth state changes
  onAuthStateChange(callback: (user: User | null) => void): () => void {
    return onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        const user = await this.convertFirebaseUser(firebaseUser);
        callback(user);
      } else {
        callback(null);
      }
    });
  }

  // Check if user has specific role
  hasRole(user: User | null, roles: UserRole[]): boolean {
    if (!user) return false;
    return roles.includes(user.role);
  }

  // Update user profile
  async updateUserProfile(uid: string, data: Partial<User>): Promise<void> {
    try {
      const updateData: any = {
        ...data,
        updated_at: serverTimestamp(),
      };

      // Remove fields that shouldn't be updated directly
      delete updateData.uid;
      delete updateData.email;
      delete updateData.role; // Role changes should be done by admin only

      await updateDoc(doc(db, 'users', uid), updateData);

      // Update Firebase Auth profile if name or photo changed
      if (auth.currentUser && auth.currentUser.uid === uid) {
        const updates: any = {};
        if (data.name) updates.displayName = data.name;
        if (data.photo_url) updates.photoURL = data.photo_url;
        
        if (Object.keys(updates).length > 0) {
          await updateProfile(auth.currentUser, updates);
        }
      }
    } catch (error) {
      console.error('Update profile error:', error);
      throw new Error('Failed to update profile');
    }
  }
}

export default new AuthService(); 