import { Timestamp } from 'firebase/firestore';

/**
 * Chat types based on the mobile app's ChatMessageModel
 * These must match exactly with the mobile app for compatibility
 */

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  SYSTEM = 'system'
}

export enum SenderType {
  CUSTOMER = 'customer',
  TECHNICIAN = 'technician',
  SYSTEM = 'system'
}

export interface ChatMessage {
  id: string;
  requestId: string;
  senderType: SenderType;
  senderId: string;
  messageType: MessageType;
  content: string;
  fileUrl?: string;
  isRead: boolean;
  createdAt: Date | Timestamp;
  updatedAt?: Date | Timestamp;
}

export interface ChatMessageInput {
  requestId: string;
  senderType: SenderType;
  senderId: string;
  messageType: MessageType;
  content: string;
  fileUrl?: string;
  isRead?: boolean;
}

export interface ChatStatus {
  active: boolean;
  lastActivity?: Date | Timestamp;
  participantCount?: number;
}

export interface ChatParticipant {
  id: string;
  name: string;
  type: SenderType;
  isOnline: boolean;
  lastSeen?: Date | Timestamp;
  avatar?: string;
}

export interface ChatRoom {
  id: string;
  requestId: string;
  participants: ChatParticipant[];
  status: ChatStatus;
  lastMessage?: ChatMessage;
  unreadCount: number;
  createdAt: Date | Timestamp;
  updatedAt?: Date | Timestamp;
}

export interface TypingIndicator {
  userId: string;
  userName: string;
  isTyping: boolean;
  timestamp: Date | Timestamp;
}



// Chat events for real-time updates
export interface ChatEvent {
  type: 'message' | 'typing' | 'read' | 'user_joined' | 'user_left' | 'chat_activated' | 'chat_deactivated';
  data: any;
  timestamp: Date | Timestamp;
  requestId: string;
}

// Chat notification types
export interface ChatNotification {
  id: string;
  type: 'new_message' | 'chat_started' | 'chat_ended' | 'file_shared';
  title: string;
  body: string;
  requestId: string;
  senderId?: string;
  senderName?: string;
  createdAt: Date | Timestamp;
  isRead: boolean;
}

// Chat settings and preferences
export interface ChatSettings {
  soundEnabled: boolean;
  desktopNotifications: boolean;
  emailNotifications: boolean;
  autoMarkAsRead: boolean;
  typingIndicators: boolean;
  fileUploadMaxSize: number; // in MB
  allowedFileTypes: string[];
}

// Chat statistics for analytics
export interface ChatStats {
  totalMessages: number;
  totalFiles: number;
  averageResponseTime: number; // in minutes
  sessionDuration: number; // in minutes
  participantCount: number;
  messagesPerParticipant: Record<string, number>;
}

// Error types for chat operations
export interface ChatError {
  code: string;
  message: string;
  details?: any;
}

// Chat service response types
export interface ChatServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ChatError;
}

// Real-time database structure types (matching mobile app)
export interface RealtimeMessage {
  id: string;
  request_id: string;
  sender_type: string;
  sender_id: string;
  message_type: string;
  content: string;
  file_url?: string;
  is_read: boolean;
  created_at: number; // timestamp
}

export interface RealtimeChatStatus {
  active: boolean;
  last_activity: number; // timestamp
  participant_count?: number;
}

export interface RealtimeTypingStatus {
  [userId: string]: {
    is_typing: boolean;
    timestamp: number;
    user_name: string;
  };
}

// Utility types for chat operations
export type ChatMessageWithUser = ChatMessage & {
  senderName: string;
  senderAvatar?: string;
};

export type ChatRoomWithMessages = ChatRoom & {
  messages: ChatMessage[];
  typingUsers: TypingIndicator[];
};
