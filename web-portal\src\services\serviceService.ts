import { where, orderBy, type QueryConstraint } from 'firebase/firestore';
import { FirebaseService, type QueryOptions } from './firebaseService';
import { type ServiceModel, type CreateServiceInput, type UpdateServiceInput } from '../types/service';

class ServiceService extends FirebaseService<ServiceModel> {
  constructor() {
    super('services');
  }

  // Get active services
  async getActiveServices(options?: QueryOptions): Promise<ServiceModel[]> {
    const constraints: QueryConstraint[] = [
      where('is_active', '==', true),
    ];

    if (options?.orderBy) {
      constraints.push(
        orderBy(options.orderBy.field, options.orderBy.direction)
      );
    } else {
      constraints.push(orderBy('category', 'asc'), orderBy('name', 'asc'));
    }

    return this.query(constraints);
  }

  // Get services by category
  async getByCategory(category: string, activeOnly: boolean = true): Promise<ServiceModel[]> {
    const constraints: QueryConstraint[] = [
      where('category', '==', category),
    ];

    if (activeOnly) {
      constraints.push(where('is_active', '==', true));
    }

    constraints.push(orderBy('name', 'asc'));

    return this.query(constraints);
  }

  // Get all categories
  async getCategories(): Promise<string[]> {
    const services = await this.getActiveServices();
    const categories = [...new Set(services.map(service => service.category))];
    return categories.sort();
  }

  // Create a new service
  async createService(data: CreateServiceInput): Promise<ServiceModel> {
    const serviceData = {
      ...data,
      is_active: data.is_active ?? true,
    };

    return this.create(serviceData as Omit<ServiceModel, 'id'>);
  }

  // Update service
  async updateService(id: string, data: UpdateServiceInput): Promise<ServiceModel> {
    return this.update(id, data);
  }

  // Toggle service active status
  async toggleActiveStatus(id: string): Promise<ServiceModel> {
    const service = await this.getById(id);
    if (!service) {
      throw new Error('Service not found');
    }

    return this.update(id, { is_active: !service.is_active });
  }

  // Get translated name
  getTranslatedName(service: ServiceModel, languageCode: string): string {
    if (typeof service.name === 'string') {
      return service.name;
    }

    // Return translation for the specified language code, or default to English, or first available
    return service.name[languageCode] || 
           service.name['en'] || 
           Object.values(service.name)[0] || 
           'Unnamed Service';
  }

  // Get translated description
  getTranslatedDescription(service: ServiceModel, languageCode: string): string {
    if (typeof service.description === 'string') {
      return service.description;
    }

    // Return translation for the specified language code, or default to English, or first available
    return service.description[languageCode] || 
           service.description['en'] || 
           Object.values(service.description)[0] || 
           'No description available';
  }

  // Search services by name or description
  async searchServices(searchTerm: string, languageCode: string = 'en'): Promise<ServiceModel[]> {
    // For now, we'll get all active services and filter client-side
    // Firestore doesn't support full-text search natively
    const services = await this.getActiveServices();
    
    const lowerSearchTerm = searchTerm.toLowerCase();
    
    return services.filter(service => {
      const name = this.getTranslatedName(service, languageCode).toLowerCase();
      const description = this.getTranslatedDescription(service, languageCode).toLowerCase();
      
      return name.includes(lowerSearchTerm) || description.includes(lowerSearchTerm);
    });
  }

  // Get services within a price range
  async getByPriceRange(minPrice: number, maxPrice: number): Promise<ServiceModel[]> {
    const constraints: QueryConstraint[] = [
      where('base_price', '>=', minPrice),
      where('base_price', '<=', maxPrice),
      where('is_active', '==', true),
      orderBy('base_price', 'asc'),
    ];

    return this.query(constraints);
  }
}

// Export singleton instance
export default new ServiceService(); 