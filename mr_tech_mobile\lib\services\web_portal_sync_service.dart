import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/enhanced_review_model.dart';
// import '../models/review_model.dart'; // TODO: Fix constructor issues

/// Service to sync enhanced ratings between mobile app and web portal
class WebPortalSyncService {
  static final WebPortalSyncService _instance =
      WebPortalSyncService._internal();
  factory WebPortalSyncService() => _instance;
  WebPortalSyncService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Sync enhanced review to web portal format
  Future<void> syncEnhancedReviewToPortal(
    EnhancedReviewModel enhancedReview,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // Create a traditional review for backward compatibility
      final traditionalReview = ReviewModel(
        id: enhancedReview.id,
        requestId: enhancedReview.requestId,
        customerId: enhancedReview.customerId,
        customerName: enhancedReview.customerName,
        technicianId: enhancedReview.technicianId,
        serviceId: enhancedReview.serviceId,
        rating: enhancedReview.overallRating,
        comment: enhancedReview.comment,
        createdAt: enhancedReview.createdAt,
      );

      // Store traditional review for web portal compatibility
      await _firestore
          .collection('reviews')
          .doc(enhancedReview.id)
          .set(traditionalReview.toFirestore());

      // Store enhanced review data separately
      await _firestore
          .collection('enhanced_reviews')
          .doc(enhancedReview.id)
          .set(enhancedReview.toFirestore());

      // Update request with rating summary
      await _firestore
          .collection('service_requests')
          .doc(enhancedReview.requestId)
          .update({
            'rating': enhancedReview.overallRating,
            'enhanced_rating_submitted': true,
            'rating_breakdown': enhancedReview.categoryRatings,
            'rating_tags': enhancedReview.tags,
            'updated_at': FieldValue.serverTimestamp(),
          });

      print('Enhanced review synced to web portal successfully');
    } catch (e) {
      print('Error syncing enhanced review to web portal: $e');
      rethrow;
    }
  }

  /// Get enhanced review from portal
  Future<EnhancedReviewModel?> getEnhancedReviewFromPortal(
    String reviewId,
  ) async {
    try {
      final doc =
          await _firestore.collection('enhanced_reviews').doc(reviewId).get();

      if (doc.exists && doc.data() != null) {
        return EnhancedReviewModel.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting enhanced review from portal: $e');
      return null;
    }
  }

  /// Stream enhanced reviews for a technician (for web portal)
  Stream<List<EnhancedReviewModel>> streamTechnicianEnhancedReviews(
    String technicianId,
  ) {
    return _firestore
        .collection('enhanced_reviews')
        .where('technician_id', isEqualTo: technicianId)
        .orderBy('created_at', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => EnhancedReviewModel.fromMap(doc.data()))
                  .toList(),
        );
  }

  /// Get rating statistics for web portal dashboard
  Future<Map<String, dynamic>> getTechnicianRatingStats(
    String technicianId,
  ) async {
    try {
      final reviews =
          await _firestore
              .collection('enhanced_reviews')
              .where('technician_id', isEqualTo: technicianId)
              .get();

      if (reviews.docs.isEmpty) {
        return {
          'total_reviews': 0,
          'average_overall': 0.0,
          'average_communication': 0.0,
          'average_technical_skill': 0.0,
          'average_timeliness': 0.0,
          'average_professionalism': 0.0,
          'average_problem_solving': 0.0,
          'positive_tags': <String, int>{},
          'negative_tags': <String, int>{},
        };
      }

      final enhancedReviews =
          reviews.docs
              .map((doc) => EnhancedReviewModel.fromMap(doc.data()))
              .toList();

      // Calculate averages
      final totalReviews = enhancedReviews.length;
      final avgOverall =
          enhancedReviews.map((r) => r.overallRating).reduce((a, b) => a + b) /
          totalReviews;
      final avgCommunication =
          enhancedReviews
              .map((r) => r.communicationRating)
              .reduce((a, b) => a + b) /
          totalReviews;
      final avgTechnicalSkill =
          enhancedReviews
              .map((r) => r.technicalSkillRating)
              .reduce((a, b) => a + b) /
          totalReviews;
      final avgTimeliness =
          enhancedReviews
              .map((r) => r.timelinessRating)
              .reduce((a, b) => a + b) /
          totalReviews;
      final avgProfessionalism =
          enhancedReviews
              .map((r) => r.professionalismRating)
              .reduce((a, b) => a + b) /
          totalReviews;
      final avgProblemSolving =
          enhancedReviews
              .map((r) => r.problemSolvingRating)
              .reduce((a, b) => a + b) /
          totalReviews;

      // Count tags
      final Map<String, int> positiveTags = {};
      final Map<String, int> negativeTags = {};

      for (final review in enhancedReviews) {
        for (final tag in review.positiveTags) {
          positiveTags[tag] = (positiveTags[tag] ?? 0) + 1;
        }
        for (final tag in review.negativeTags) {
          negativeTags[tag] = (negativeTags[tag] ?? 0) + 1;
        }
      }

      return {
        'total_reviews': totalReviews,
        'average_overall': avgOverall,
        'average_communication': avgCommunication,
        'average_technical_skill': avgTechnicalSkill,
        'average_timeliness': avgTimeliness,
        'average_professionalism': avgProfessionalism,
        'average_problem_solving': avgProblemSolving,
        'positive_tags': positiveTags,
        'negative_tags': negativeTags,
      };
    } catch (e) {
      print('Error getting technician rating stats: $e');
      return {};
    }
  }

  /// Update enhanced review (for technician responses)
  Future<void> updateEnhancedReview(
    String reviewId,
    Map<String, dynamic> updates,
  ) async {
    try {
      await _firestore.collection('enhanced_reviews').doc(reviewId).update({
        ...updates,
        'updated_at': FieldValue.serverTimestamp(),
      });

      print('Enhanced review updated successfully');
    } catch (e) {
      print('Error updating enhanced review: $e');
      rethrow;
    }
  }

  /// Add technician response to enhanced review
  Future<void> addTechnicianResponse(String reviewId, String response) async {
    try {
      await updateEnhancedReview(reviewId, {
        'technician_response': response,
        'technician_response_date': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error adding technician response: $e');
      rethrow;
    }
  }

  /// Mark review as helpful (for web portal users)
  Future<void> markReviewAsHelpful(String reviewId, String userId) async {
    try {
      await _firestore.collection('enhanced_reviews').doc(reviewId).update({
        'helpful_users': FieldValue.arrayUnion([userId]),
        'helpful_count': FieldValue.increment(1),
      });
    } catch (e) {
      print('Error marking review as helpful: $e');
      rethrow;
    }
  }

  /// Report review (for moderation)
  Future<void> reportReview(
    String reviewId,
    String reason,
    String reporterId,
  ) async {
    try {
      await _firestore.collection('review_reports').add({
        'review_id': reviewId,
        'reason': reason,
        'reporter_id': reporterId,
        'status': 'pending',
        'created_at': FieldValue.serverTimestamp(),
      });

      // Update review with report flag
      await _firestore.collection('enhanced_reviews').doc(reviewId).update({
        'reported': true,
        'report_count': FieldValue.increment(1),
      });
    } catch (e) {
      print('Error reporting review: $e');
      rethrow;
    }
  }
}
