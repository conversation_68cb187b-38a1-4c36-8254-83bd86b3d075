import 'package:flutter/material.dart';
import '../views/main_navigation.dart';

class NavigationUtils {
  // List of main screens that should have bottom navigation
  static final List<String> _mainScreens = [
    'HomeScreen',
    'DashboardScreen',
    'RequestsScreen',
    'ServicesScreen',
    'ChatsListScreen',
    'SettingsScreen',
  ];

  /// Navigate to a screen with bottom navigation bar
  /// Only use this for the main navigation screens
  static void navigateWithBottomNav(
    BuildContext context,
    Widget screen, {
    bool replace = false,
  }) {
    // Check if this is a main screen or not - if not, use regular navigation
    final screenType = screen.runtimeType.toString();

    if (_mainScreens.contains(screenType)) {
      // This is a main screen, use the MainNavigation wrapper
      if (replace) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder:
                (context) => MainNavigation(
                  replacementScreen: screen,
                  initialIndex: _getScreenIndex(screenType),
                ),
          ),
        );
      } else {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => MainNavigation(
                  replacementScreen: screen,
                  initialIndex: _getScreenIndex(screenType),
                ),
          ),
        );
      }
    } else {
      // This is not a main screen, use normal navigation without bottom bar
      if (replace) {
        Navigator.of(
          context,
        ).pushReplacement(MaterialPageRoute(builder: (context) => screen));
      } else {
        navigateKeepingStack(context, screen);
      }
    }
  }

  /// Navigate to a screen without bottom navigation bar
  /// while preserving the current navigation stack
  /// Use this for detail screens and other non-main screens
  /// If onReturn is provided, it will be called when returning from the pushed screen
  static Future<T?> navigateKeepingStack<T>(
    BuildContext context,
    Widget screen, {
    Function(T?)? onReturn,
  }) async {
    final result = await Navigator.of(
      context,
    ).push<T>(MaterialPageRoute(builder: (context) => screen));

    if (onReturn != null) {
      onReturn(result);
    }

    return result;
  }

  /// Get the index of a main screen in the bottom navigation bar
  static int _getScreenIndex(String screenType) {
    switch (screenType) {
      case 'HomeScreen':
        return 0;
      case 'RequestsScreen':
        return 1;
      case 'ServicesScreen':
        return 2;
      case 'ChatsListScreen':
        return 3;
      case 'SettingsScreen':
        return 4;
      default:
        return 0;
    }
  }

  /// Navigate to the request details screen
  /// This will need to be implemented by the actual screen
  /// For now, we'll use named routes since the actual implementations
  /// have different signatures than what we need for notifications
  static void navigateToRequestDetails(BuildContext context, String requestId) {
    // For notifications, we'll use a named route
    // The actual implementation will be handled by the app's routing system
    Navigator.of(context).pushNamed('/request_details', arguments: requestId);
  }

  /// Navigate to the chat screen
  /// This will need to be implemented by the actual screen
  /// For now, we'll use named routes
  static void navigateToChatScreen(BuildContext context, String requestId) {
    // For notifications, we'll use a named route
    // The actual implementation will be handled by the app's routing system
    Navigator.of(context).pushNamed('/chat', arguments: requestId);
  }

  /// Navigator shortcut for named routes
  static Future<T?> pushNamed<T>(
    BuildContext context,
    String routeName, {
    Object? arguments,
  }) {
    return Navigator.of(context).pushNamed<T>(routeName, arguments: arguments);
  }
}
