{"emulators": {"dataconnect": {"dataDir": "dataconnect/.dataconnect/pgliteData"}}, "dataconnect": {"source": "dataconnect"}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "database": {"rules": "database.rules.json"}, "flutter": {"platforms": {"android": {"default": {"projectId": "stopnow-be6b7", "appId": "1:586312365224:android:8861c3bf21fbb2d1bffe9d", "fileOutput": "../mr_tech_mobile/android/app/google-services.json"}}, "dart": {"../mr_tech_mobile/lib/firebase_options.dart": {"projectId": "stopnow-be6b7", "configurations": {"android": "1:586312365224:android:8861c3bf21fbb2d1bffe9d", "ios": "1:586312365224:ios:803532683120900fbffe9d"}}}}}}