import { where, orderBy, limit, startAfter, Timestamp, type QueryConstraint } from 'firebase/firestore';
import { FirebaseService, type QueryOptions } from './firebaseService';
import { 
  type RequestModel, 
  type CreateRequestInput, 
  type UpdateRequestInput, 
  RequestStatus 
} from '../types/request';

class RequestService extends FirebaseService<RequestModel> {
  constructor() {
    super('service_requests');
  }

  // Transform raw Firebase data to RequestModel with proper field mapping
  private transformRequestData(rawData: any): RequestModel {
    return {
      id: rawData.id || '',
      customer_id: rawData.customer_id || rawData.customerId || '',
      service_id: rawData.service_id || rawData.serviceId || '',
      service_name: rawData.service_name || rawData.serviceName || '',
      service_description: rawData.service_description || rawData.serviceDescription || '',
      customer_issue: rawData.customer_issue || rawData.customerIssue || '',
      anydesk_id: rawData.anydesk_id || rawData.anydeskId,
      anydeskId: rawData.anydeskId || rawData.anydesk_id, // Keep both for compatibility
      technician_id: rawData.technician_id,
      technician_name: rawData.technician_name,
      status: rawData.status || RequestStatus.PENDING,
      amount: rawData.amount || 0,
      is_paid: rawData.is_paid ?? rawData.isPaid ?? false,
      chat_active: rawData.chat_active ?? rawData.chatActive ?? false,
      session_active: rawData.session_active ?? rawData.sessionActive ?? false,
      created_at: rawData.created_at || rawData.createdAt || new Date(),
      updated_at: rawData.updated_at || rawData.updatedAt,
      scheduled_time: rawData.scheduled_time || rawData.scheduledTime,
      session_start_time: rawData.session_start_time || rawData.sessionStartTime,
      session_end_time: rawData.session_end_time || rawData.sessionEndTime,
      session_duration: rawData.session_duration || rawData.sessionDuration,
      customer_rated: rawData.customer_rated ?? rawData.customerRated,
      rating: rawData.rating,
      review_comment: rawData.review_comment || rawData.reviewComment,
      customer_fcm_token: rawData.customer_fcm_token || rawData.customerFcmToken,
      is_visible: rawData.is_visible ?? rawData.isVisible ?? true,
      customer_name: rawData.customer_name || rawData.customerName,
      customer_email: rawData.customer_email || rawData.customerEmail,
      // Legacy fields for compatibility
      customerName: rawData.customerName || rawData.customer_name,
      customerEmail: rawData.customerEmail || rawData.customer_email,
      customerFcmToken: rawData.customerFcmToken || rawData.customer_fcm_token,
      customerId: rawData.customerId || rawData.customer_id,
      customerIssue: rawData.customerIssue || rawData.customer_issue,
      serviceId: rawData.serviceId || rawData.service_id,
      serviceName: rawData.serviceName || rawData.service_name,
      serviceDescription: rawData.serviceDescription || rawData.service_description,
      createdAt: rawData.createdAt || rawData.created_at,
      updatedAt: rawData.updatedAt || rawData.updated_at,
      isPaid: rawData.isPaid ?? rawData.is_paid,
      isVisible: rawData.isVisible ?? rawData.is_visible,
      chatActive: rawData.chatActive ?? rawData.chat_active,
      sessionActive: rawData.sessionActive ?? rawData.session_active,
    };
  }

  // Override getAll to apply transformation
  async getAll(options?: QueryOptions): Promise<RequestModel[]> {
    try {
      const constraints: any[] = [];

      // Add where constraints
      if (options?.where) {
        options.where.forEach((w) => {
          constraints.push(where(w.field, w.operator, w.value));
        });
      }

      // Add orderBy constraint
      if (options?.orderBy) {
        constraints.push(
          orderBy(options.orderBy.field, options.orderBy.direction)
        );
      }

      // Add pagination constraints
      if (options?.limit) {
        constraints.push(limit(options.limit));
      }

      if (options?.startAfter) {
        constraints.push(startAfter(options.startAfter));
      }

      // Use our query method which applies transformation
      return this.query(constraints);
    } catch (error) {
      console.error('Error getting all requests:', error);
      throw error;
    }
  }

  // Override getById to apply transformation
  async getById(id: string): Promise<RequestModel | null> {
    const rawRequest = await super.getById(id);
    if (!rawRequest) return null;
    return this.transformRequestData(rawRequest);
  }

  // Override query to apply transformation
  async query(constraints: any[]): Promise<RequestModel[]> {
    const rawRequests = await super.query(constraints);
    return rawRequests.map(request => this.transformRequestData(request));
  }

  // Get requests by customer ID
  async getByCustomerId(customerId: string, options?: QueryOptions): Promise<RequestModel[]> {
    const constraints: QueryConstraint[] = [
      where('customer_id', '==', customerId),
      where('is_visible', '!=', false),
    ];

    if (options?.orderBy) {
      constraints.push(
        orderBy(options.orderBy.field, options.orderBy.direction)
      );
    } else {
      constraints.push(orderBy('created_at', 'desc'));
    }

    const rawRequests = await super.query(constraints);
    return rawRequests.map(request => this.transformRequestData(request));
  }

  // Get requests by technician ID
  async getByTechnicianId(technicianId: string, options?: QueryOptions): Promise<RequestModel[]> {
    const constraints = [
      where('technician_id', '==', technicianId),
      where('is_visible', '!=', false),
    ];

    if (options?.orderBy) {
      constraints.push(
        orderBy(options.orderBy.field, options.orderBy.direction)
      );
    } else {
      constraints.push(orderBy('created_at', 'desc'));
    }

    return this.query(constraints);
  }

  // Get requests by status
  async getByStatus(status: RequestStatus, options?: QueryOptions): Promise<RequestModel[]> {
    const constraints = [
      where('status', '==', status),
      where('is_visible', '!=', false),
    ];

    if (options?.orderBy) {
      constraints.push(
        orderBy(options.orderBy.field, options.orderBy.direction)
      );
    } else {
      constraints.push(orderBy('created_at', 'desc'));
    }

    return this.query(constraints);
  }

  // Get pending requests (not assigned to any technician)
  async getPendingRequests(options?: QueryOptions): Promise<RequestModel[]> {
    const constraints = [
      where('status', '==', RequestStatus.PENDING),
      where('technician_id', '==', null),
      where('is_visible', '!=', false),
    ];

    if (options?.orderBy) {
      constraints.push(
        orderBy(options.orderBy.field, options.orderBy.direction)
      );
    } else {
      constraints.push(orderBy('created_at', 'asc')); // Oldest first
    }

    const rawRequests = await super.query(constraints);
    return rawRequests.map(request => this.transformRequestData(request));
  }

  // Get active requests (in progress)
  async getActiveRequests(options?: QueryOptions): Promise<RequestModel[]> {
    const constraints = [
      where('status', 'in', [RequestStatus.APPROVED, RequestStatus.IN_PROGRESS]),
      where('is_visible', '!=', false),
    ];

    if (options?.orderBy) {
      constraints.push(
        orderBy(options.orderBy.field, options.orderBy.direction)
      );
    } else {
      constraints.push(orderBy('created_at', 'desc'));
    }

    const rawRequests = await super.query(constraints);
    return rawRequests.map(request => this.transformRequestData(request));
  }

  // Create a new request
  async createRequest(data: CreateRequestInput): Promise<RequestModel> {
    const requestData = {
      ...data,
      status: RequestStatus.PAYMENT_PENDING,
      is_paid: false,
      chat_active: false,
      session_active: false,
      is_visible: true,
      created_at: Timestamp.now(),
    };

    return this.create(requestData as Omit<RequestModel, 'id'>);
  }

  // Update request status
  async updateStatus(id: string, status: RequestStatus): Promise<RequestModel> {
    const updateData: Partial<RequestModel> = { status };

    // Handle status-specific updates
    switch (status) {
      case RequestStatus.IN_PROGRESS:
        if (!updateData.session_start_time) {
          updateData.session_start_time = Timestamp.now();
        }
        updateData.session_active = true;
        break;
      
      case RequestStatus.COMPLETED:
        if (!updateData.session_end_time) {
          updateData.session_end_time = Timestamp.now();
        }
        updateData.session_active = false;
        break;
      
      case RequestStatus.CANCELLED:
      case RequestStatus.REFUSED:
        updateData.session_active = false;
        updateData.chat_active = false;
        break;
    }

    return this.update(id, updateData);
  }

  // Assign technician to request
  async assignTechnician(
    requestId: string, 
    technicianId: string, 
    technicianName: string
  ): Promise<RequestModel> {
    return this.update(requestId, {
      technician_id: technicianId,
      technician_name: technicianName,
      status: RequestStatus.APPROVED,
    });
  }

  // Update payment status
  async updatePaymentStatus(id: string, isPaid: boolean): Promise<RequestModel> {
    const updateData: Partial<RequestModel> = { 
      is_paid: isPaid,
    };

    // If payment is completed and status is payment_pending, update to pending
    if (isPaid) {
      const request = await this.getById(id);
      if (request?.status === RequestStatus.PAYMENT_PENDING) {
        updateData.status = RequestStatus.PENDING;
      }
    }

    return this.update(id, updateData);
  }

  // Toggle chat active status
  async toggleChatActive(id: string, isActive: boolean): Promise<RequestModel> {
    return this.update(id, { chat_active: isActive });
  }

  // Add rating and review
  async addRating(
    id: string, 
    rating: number, 
    reviewComment?: string
  ): Promise<RequestModel> {
    return this.update(id, {
      customer_rated: true,
      rating,
      review_comment: reviewComment,
    });
  }

  // Calculate session duration
  async calculateSessionDuration(id: string): Promise<RequestModel> {
    const request = await this.getById(id);
    if (!request) {
      throw new Error('Request not found');
    }

    if (request.session_start_time && request.session_end_time) {
      const startTime = this.toDate(request.session_start_time);
      const endTime = this.toDate(request.session_end_time);
      
      if (startTime && endTime) {
        const durationInMinutes = Math.round(
          (endTime.getTime() - startTime.getTime()) / (1000 * 60)
        );
        
        return this.update(id, { session_duration: durationInMinutes });
      }
    }

    return request;
  }
}

// Export singleton instance
export default new RequestService(); 